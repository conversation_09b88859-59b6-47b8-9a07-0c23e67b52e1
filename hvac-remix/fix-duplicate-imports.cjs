#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function fixDuplicateImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    // Track imports we've seen
    const seenImports = new Set();
    const fixedLines = [];
    let hasChanges = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Check if this is an import line
      if (line.trim().startsWith('import ')) {
        // Extract the import statement (handle multi-line imports)
        let fullImport = line;
        let j = i;
        
        // If the line doesn't end with ';' and doesn't contain 'from', it might be multi-line
        while (!fullImport.includes(';') && !fullImport.includes(' from ') && j < lines.length - 1) {
          j++;
          fullImport += '\n' + lines[j];
        }
        
        // Normalize the import for comparison
        const normalizedImport = fullImport.replace(/\s+/g, ' ').trim();
        
        if (seenImports.has(normalizedImport)) {
          // Skip this duplicate import
          hasChanges = true;
          // Skip all lines that were part of this import
          i = j;
          continue;
        } else {
          seenImports.add(normalizedImport);
          // Add all lines that were part of this import
          for (let k = i; k <= j; k++) {
            fixedLines.push(lines[k]);
          }
          i = j;
        }
      } else {
        fixedLines.push(line);
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, fixedLines.join('\n'));
      console.log(`Fixed duplicate imports in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

function findTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
const appDir = path.join(__dirname, 'app');
const files = findTsxFiles(appDir);

console.log(`Found ${files.length} TypeScript files to check...`);

let fixedCount = 0;
for (const file of files) {
  if (fixDuplicateImports(file)) {
    fixedCount++;
  }
}

console.log(`Fixed duplicate imports in ${fixedCount} files.`);