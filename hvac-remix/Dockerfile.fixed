# HVAC-Remix Fixed Dockerfile for CopilotKit Integration
FROM node:18-alpine

WORKDIR /app

# Install dependencies with legacy peer deps to avoid conflicts
COPY package*.json ./
RUN npm ci --legacy-peer-deps

# Install CopilotKit dependencies
RUN npm install @copilotkit/react-core @copilotkit/react-ui @copilotkit/runtime --legacy-peer-deps

# Copy source code
COPY . .

# Create uploads directory
RUN mkdir -p uploads public/uploads

# Build the application
RUN npm run build

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["npm", "start"]