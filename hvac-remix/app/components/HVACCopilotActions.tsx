/**
 * HVAC-Specific CopilotKit Actions
 * Defines all AI actions available in the HVAC CRM system
 */

import { useCopilotAction } from '@copilotkit/react-core';
import { getCopilotKitBridge } from '~/services/copilotkit-bridge.server';
import type { ServiceOrderData } from '~/types/copilotkit-bridge';

export function HVACCopilotActions() {
  const bridge = getCopilotKitBridge();

  // Customer Issue Analysis Action
  useCopilotAction({
    name: "analyzeCustomerIssue",
    description: "Analyze customer HVAC issue and provide comprehensive recommendations with technical insights",
    parameters: {
      type: "object",
      properties: {
        customerMessage: {
          type: "string",
          description: "Customer's description of the HVAC issue or problem"
        },
        customerId: {
          type: "string",
          description: "Customer ID for context enrichment (optional)"
        },
        urgency: {
          type: "string",
          enum: ["low", "medium", "high", "critical"],
          description: "Urgency level of the issue",
          default: "medium"
        }
      },
      required: ["customerMessage"]
    },
    handler: async ({ customerMessage, customerId, urgency = "medium" }) => {
      try {
        const analysis = await bridge.analyzeCustomerIssue(
          customerMessage,
          customerId,
          urgency
        );

        return {
          success: true,
          analysis: analysis.analysis,
          recommendations: analysis.recommendations,
          similar_cases: analysis.similar_cases,
          technical_insights: analysis.technical_insights,
          confidence: analysis.confidence,
          message: `Issue analyzed with ${(analysis.confidence * 100).toFixed(1)}% confidence. 
                   Intent: ${analysis.analysis?.intent || 'unknown'}, 
                   Urgency: ${analysis.analysis?.urgency_level || urgency}`
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Analysis failed',
          message: "Failed to analyze customer issue. Please try again or contact support."
        };
      }
    }
  });

  // Memory Bank Search Action
  useCopilotAction({
    name: "searchCustomerHistory",
    description: "Search customer interaction history and service records for insights and patterns",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query for customer history and interactions"
        },
        customerId: {
          type: "string",
          description: "Specific customer ID to search (optional)"
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return",
          default: 10,
          minimum: 1,
          maximum: 50
        }
      },
      required: ["query"]
    },
    handler: async ({ query, customerId, limit = 10 }) => {
      try {
        const results = await bridge.searchMemoryBank(query, customerId, limit);

        return {
          success: true,
          results: results.results,
          insights: results.insights,
          patterns: results.patterns,
          total_found: results.total,
          message: `Found ${results.total} relevant records in ${results.query_time_ms}ms`
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Search failed',
          message: "Failed to search customer history. Please try again."
        };
      }
    }
  });

  // Bielik V3 Consultation Action
  useCopilotAction({
    name: "askBielikExpert",
    description: "Consult Bielik V3 AI model for expert HVAC technical analysis and recommendations",
    parameters: {
      type: "object",
      properties: {
        question: {
          type: "string",
          description: "Technical question or problem description for Bielik V3"
        },
        context: {
          type: "string",
          description: "Additional context or background information (optional)"
        },
        analysisType: {
          type: "string",
          enum: ["semantic_analysis", "intent_detection", "technical_classification"],
          description: "Type of analysis to perform",
          default: "technical_classification"
        }
      },
      required: ["question"]
    },
    handler: async ({ question, context, analysisType = "technical_classification" }) => {
      try {
        const response = await bridge.askBielik(
          question,
          context ? { additional_context: context } : undefined,
          analysisType
        );

        return {
          success: true,
          expert_response: response.response,
          confidence: response.confidence,
          analysis_type: response.task_type,
          processing_time: response.processing_time_ms,
          language_detected: response.language_detected,
          message: `Bielik V3 analysis completed in ${response.processing_time_ms}ms with ${(response.confidence * 100).toFixed(1)}% confidence`
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Bielik consultation failed',
          message: "Failed to get expert analysis from Bielik V3. Please try again."
        };
      }
    }
  });

  // Professional Response Generation Action
  useCopilotAction({
    name: "generateProfessionalResponse",
    description: "Generate professional email response for customer communications with appropriate tone and scheduling options",
    parameters: {
      type: "object",
      properties: {
        emailContext: {
          type: "object",
          description: "Email context including customer message and analysis results"
        },
        tone: {
          type: "string",
          enum: ["professional", "friendly", "urgent", "apologetic"],
          description: "Tone for the response",
          default: "professional"
        },
        includeScheduling: {
          type: "boolean",
          description: "Include scheduling options in the response",
          default: false
        }
      },
      required: ["emailContext"]
    },
    handler: async ({ emailContext, tone = "professional", includeScheduling = false }) => {
      try {
        const response = await bridge.generateResponse(
          emailContext,
          tone,
          includeScheduling
        );

        return {
          success: true,
          draft_response: response.draft_response,
          suggested_actions: response.suggested_actions,
          scheduling_options: response.scheduling_options,
          confidence: response.confidence,
          message: `Professional response generated with ${tone} tone and ${(response.confidence * 100).toFixed(1)}% confidence`
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Response generation failed',
          message: "Failed to generate professional response. Please try again."
        };
      }
    }
  });

  // Service Route Optimization Action
  useCopilotAction({
    name: "optimizeServiceRoute",
    description: "Optimize technician service routes for maximum efficiency and cost savings",
    parameters: {
      type: "object",
      properties: {
        serviceOrders: {
          type: "array",
          description: "Array of service orders to optimize",
          items: {
            type: "object",
            properties: {
              order_id: { type: "string" },
              customer_id: { type: "string" },
              address: { type: "string" },
              service_type: { type: "string" },
              priority: { type: "string", enum: ["low", "medium", "high", "emergency"] },
              estimated_duration: { type: "number" }
            }
          }
        },
        technicianId: {
          type: "string",
          description: "Technician ID for route optimization (optional)"
        }
      },
      required: ["serviceOrders"]
    },
    handler: async ({ serviceOrders, technicianId }) => {
      try {
        const optimization = await bridge.optimizeServiceRoute(serviceOrders as ServiceOrderData[]);

        return {
          success: true,
          optimized_route: optimization.optimized_route,
          estimated_time: optimization.estimated_time,
          cost_savings: optimization.cost_savings,
          technician_id: technicianId,
          message: `Route optimized with ${optimization.cost_savings}% cost savings and ${optimization.estimated_time} minutes total time`
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Route optimization failed',
          message: "Failed to optimize service route. Please try again."
        };
      }
    }
  });

  // Predictive Maintenance Action
  useCopilotAction({
    name: "predictMaintenanceNeeds",
    description: "Predict future maintenance needs for HVAC equipment using AI analysis",
    parameters: {
      type: "object",
      properties: {
        deviceId: {
          type: "string",
          description: "HVAC device/equipment ID"
        },
        deviceData: {
          type: "object",
          description: "Device information including type, model, age, service history"
        }
      },
      required: ["deviceId", "deviceData"]
    },
    handler: async ({ deviceId, deviceData }) => {
      try {
        const prediction = await bridge.predictMaintenanceNeeds(deviceId, deviceData);

        return {
          success: true,
          predictions: prediction.predictions,
          recommended_actions: prediction.recommended_actions,
          timeline: prediction.timeline,
          confidence: prediction.confidence,
          device_id: deviceId,
          message: `Maintenance predictions generated with ${(prediction.confidence * 100).toFixed(1)}% confidence for device ${deviceId}`
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Maintenance prediction failed',
          message: "Failed to predict maintenance needs. Please try again."
        };
      }
    }
  });

  // Document Analysis Action
  useCopilotAction({
    name: "analyzeDocument",
    description: "Analyze HVAC documents including manuals, invoices, photos, and service reports",
    parameters: {
      type: "object",
      properties: {
        documentPath: {
          type: "string",
          description: "Path to the document file"
        },
        documentType: {
          type: "string",
          enum: ["manual", "invoice", "photo", "report"],
          description: "Type of document to analyze"
        },
        metadata: {
          type: "object",
          description: "Additional metadata about the document (optional)"
        }
      },
      required: ["documentPath", "documentType"]
    },
    handler: async ({ documentPath, documentType, metadata }) => {
      try {
        const analysis = await bridge.analyzeDocument(documentPath, documentType, metadata);

        return {
          success: true,
          analysis: analysis.analysis,
          extracted_data: analysis.extracted_data,
          confidence: analysis.confidence,
          processing_time: analysis.processing_time,
          document_type: documentType,
          message: `Document analyzed in ${analysis.processing_time}ms with ${(analysis.confidence * 100).toFixed(1)}% confidence`
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Document analysis failed',
          message: "Failed to analyze document. Please check the file path and try again."
        };
      }
    }
  });

  // System Status Check Action
  useCopilotAction({
    name: "checkSystemStatus",
    description: "Check the health and status of all TruBackend AI services",
    parameters: {
      type: "object",
      properties: {},
      required: []
    },
    handler: async () => {
      try {
        const status = await bridge.getTruBackendStatus();

        return {
          success: true,
          overall_status: status.overall_status,
          healthy_services: status.healthy_services,
          total_services: status.total_services,
          services: status.services,
          timestamp: status.timestamp,
          message: `System Status: ${status.healthy_services}/${status.total_services} services healthy`
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Status check failed',
          message: "Failed to check system status. Some services may be unavailable."
        };
      }
    }
  });

  return null; // Actions are registered, no UI component needed
}