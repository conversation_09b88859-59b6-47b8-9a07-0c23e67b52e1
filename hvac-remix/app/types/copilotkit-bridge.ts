/**
 * TypeScript types for CopilotKit Bridge Integration
 */

// Email Intelligence Types
export interface EmailData {
  from: string;
  to?: string;
  subject: string;
  content: string;
  timestamp: string;
  customer_id?: string;
  urgency_level?: 'low' | 'medium' | 'high' | 'critical';
}

export interface EmailAnalysis {
  semantic_analysis: {
    intent: string;
    sentiment: string;
    urgency_level: string;
    category: string;
    keywords: string[];
    entities: any[];
  };
  customer_context?: {
    name: string;
    history: any[];
    risk_factors: string[];
    preferences: any;
  };
  response_suggestion: {
    tone: string;
    estimated_resolution_time: string;
    draft: string;
    priority_actions: string[];
  };
  confidence_score: number;
  processing_time_ms: number;
}

// Memory Bank Types
export interface MemoryResults {
  results: MemoryItem[];
  insights: string[];
  patterns: any[];
  total: number;
  query_time_ms: number;
}

export interface MemoryItem {
  id: string;
  content: string;
  metadata: any;
  similarity_score: number;
  timestamp: string;
  customer_id?: string;
}

// Bielik Integration Types
export interface BielikResponse {
  response: string;
  confidence: number;
  task_type: string;
  processing_time_ms: number;
  language_detected?: 'pl' | 'en';
}

// Customer Context Types
export interface CustomerContext {
  customer_id: string;
  interaction_history: MemoryItem[];
  service_history: any[];
  patterns: string;
  risk_factors: string[];
  preferences: any;
  last_updated: string;
}

// Service Order Types
export interface ServiceOrderData {
  order_id: string;
  customer_id: string;
  technician_id?: string;
  location: {
    address: string;
    coordinates?: [number, number];
  };
  service_type: string;
  priority: 'low' | 'medium' | 'high' | 'emergency';
  estimated_duration: number;
  required_skills: string[];
  equipment_needed: string[];
  scheduled_date?: string;
}

// Document Analysis Types
export interface DocumentAnalysisRequest {
  document_path: string;
  document_type: 'manual' | 'invoice' | 'photo' | 'report';
  metadata?: any;
}

// System Health Types
export interface TruBackendHealthStatus {
  overall_status: 'all_healthy' | 'partial_issues' | 'critical';
  healthy_services: number;
  total_services: number;
  services: ServiceHealthStatus[];
  timestamp: string;
}

export interface ServiceHealthStatus {
  name: string;
  status: 'healthy' | 'unhealthy' | 'unreachable' | 'error';
  url: string;
  response_time?: number;
  error?: string;
}

// CopilotKit Action Types
export interface CopilotKitActionResult {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
  confidence?: number;
  processing_time?: number;
}

// HVAC-specific Types
export interface HVACIssueAnalysis {
  issue_type: string;
  severity: 'minor' | 'moderate' | 'major' | 'critical';
  equipment_affected: string[];
  recommended_actions: string[];
  estimated_cost: number;
  urgency_timeline: string;
  safety_concerns: string[];
  parts_needed: string[];
}

export interface MaintenancePrediction {
  equipment_id: string;
  predicted_issues: {
    issue: string;
    probability: number;
    timeline: string;
    cost_estimate: number;
  }[];
  recommended_schedule: {
    task: string;
    frequency: string;
    next_due: string;
  }[];
  risk_assessment: string;
}

export interface RouteOptimization {
  optimized_route: {
    order: number;
    service_order_id: string;
    estimated_arrival: string;
    travel_time: number;
  }[];
  total_time: number;
  total_distance: number;
  cost_savings: number;
  efficiency_improvement: number;
}

// Configuration Types
export interface CopilotKitBridgeConfig {
  trubackend_base_url: string;
  email_intelligence_url: string;
  memory_bank_url: string;
  bielik_integration_url: string;
  executive_assistant_url: string;
  langchain_automation_url: string;
  timeout: number;
  retries: number;
  api_key?: string;
}