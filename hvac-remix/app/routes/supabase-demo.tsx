import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { supabase } from "~/supabase.server";
import { prisma } from "~/db.server";
import RealTimeUpdates from "~/components/RealTimeUpdates";
import { useEffect, useState } from "react";
import { createBucketIfNotExists } from "~/utils/supabase";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Create storage buckets if they don't exist
    await createBucketIfNotExists('documents', true);
    await createBucketIfNotExists('images', true);
    
    // Get storage buckets
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    // Get recent customers using Prisma
    const customers = await prisma.customer.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        createdAt: true,
      },
    });
    
    return json({
      buckets: {
        data: buckets,
        error: bucketsError ? bucketsError.message : null,
      },
      customers,
    });
  } catch (error) {
    console.error("Error in loader:", error);
    return json({
      buckets: {
        data: null,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      customers: [],
    });
  }
}export default function SupabaseDemo() {
  const data = useLoaderData<typeof loader>();
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  
  // Function to perform text search
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    try {
      const { data, error } = await supabase
        .from('Customer')
        .select('id, name, email, phone, createdAt')
        .textSearch('name', searchQuery)
        .limit(10);
      
      if (error) throw error;
      setSearchResults(data || []);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };
  
  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Supabase Features Demo</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Storage Buckets */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Storage Buckets</h2>
          {data.buckets.error ? (
            <p className="text-red-600">Error: {data.buckets.error}</p>
          ) : (
            <ul className="space-y-2">
              {data.buckets.data?.map((bucket) => (
                <li key={bucket.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <span className="font-medium">{bucket.name}</span>
                    <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {bucket.public ? 'Public' : 'Private'}
                    </span>
                  </div>
                  <span className="text-sm text-gray-500">
                    Created: {new Date(bucket.created_at).toLocaleDateString()}
                  </span>
                </li>
              ))}
              {!data.buckets.data?.length && (
                <p className="text-gray-500">No storage buckets found.</p>
              )}
            </ul>
          )}
        </div>        
        {/* Text Search */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Full-Text Search</h2>
          <div className="flex mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search customers by name..."
              className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handleSearch}
              disabled={isSearching}
              className="bg-blue-600 text-white px-4 py-2 rounded-r hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSearching ? 'Searching...' : 'Search'}
            </button>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">Results:</h3>
            {searchResults.length > 0 ? (
              <ul className="space-y-2">
                {searchResults.map((customer) => (
                  <li key={customer.id} className="p-3 bg-gray-50 rounded">
                    <div className="font-medium">{customer.name}</div>
                    <div className="text-sm text-gray-600">{customer.email}</div>
                    <div className="text-sm text-gray-600">{customer.phone}</div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500">
                {searchQuery && !isSearching ? 'No results found.' : 'Enter a search term and click Search.'}
              </p>
            )}
          </div>
        </div>
        
        {/* Recent Customers */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Recent Customers</h2>
          {data.customers.length > 0 ? (
            <ul className="space-y-2">
              {data.customers.map((customer) => (
                <li key={customer.id} className="p-3 bg-gray-50 rounded">
                  <div className="font-medium">{customer.name}</div>
                  <div className="text-sm text-gray-600">{customer.email}</div>
                  <div className="text-sm text-gray-600">{customer.phone}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    Added: {new Date(customer.createdAt).toLocaleDateString()}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">No customers found.</p>
          )}
        </div>        
        {/* Real-time Updates */}
        <div className="bg-white shadow rounded-lg p-6">
          <RealTimeUpdates table="Customer" title="Real-time Customer Updates" />
          <p className="text-sm text-gray-500 mt-4">
            Make changes to the Customer table to see real-time updates here.
          </p>
        </div>
      </div>
    </div>
  );
}