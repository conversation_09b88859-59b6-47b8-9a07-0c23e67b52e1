import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { initializeAugmentSupabase, searchEmbeddings, storeEmbedding, synchronizeData } from "~/services/augment-supabase.server";
import { supabase } from "~/supabase.server";
import { requireUser } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // Ensure user is authenticated
  await requireUser(request);
  
  try {
    // Initialize Augment-Supabase integration
    await initializeAugmentSupabase();
    
    // Get vector embeddings
    const { data: embeddings, error: embeddingsError } = await supabase
      .from('vector_embeddings')
      .select('id, object_id, object_type, content, created_at')
      .order('created_at', { ascending: false })
      .limit(10);
    
    // Get file metadata
    const { data: files, error: filesError } = await supabase
      .from('file_metadata')
      .select('id, bucket, path, size, mime_type, public_url, created_at')
      .order('created_at', { ascending: false })
      .limit(10);
    
    // Get logs
    const { data: logs, error: logsError } = await supabase
      .from('augment_logs')
      .select('id, action, user_id, details, created_at')
      .order('created_at', { ascending: false })
      .limit(10);
    
    return json({
      embeddings: {
        data: embeddings || [],
        error: embeddingsError ? embeddingsError.message : null,
      },
      files: {
        data: files || [],
        error: filesError ? filesError.message : null,
      },
      logs: {
        data: logs || [],
        error: logsError ? logsError.message : null,
      },
    });
  } catch (error) {
    console.error("Error in loader:", error);
    return json({
      embeddings: { data: [], error: error instanceof Error ? error.message : "Unknown error" },
      files: { data: [], error: error instanceof Error ? error.message : "Unknown error" },
      logs: { data: [], error: error instanceof Error ? error.message : "Unknown error" },
    });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  // Ensure user is authenticated
  const user = await requireUser(request);
  
  const formData = await request.formData();
  const action = formData.get("action") as string;
  
  try {
    switch (action) {
      case "store-embedding": {
        const objectId = formData.get("objectId") as string;
        const objectType = formData.get("objectType") as string;
        const content = formData.get("content") as string;
        
        // In a real application, you would generate embeddings using an AI model
        // For this demo, we'll use a simple mock embedding
        const mockEmbedding = Array.from({ length: 1536 }, () => Math.random() * 2 - 1);
        
        const embeddingId = await storeEmbedding(objectId, objectType, mockEmbedding, content);
        
        // Log the action
        await supabase.rpc('log_augment_action', {
          action: 'store_embedding',
          user_id: user.id,
          details: { objectId, objectType, embeddingId }
        });
        
        return json({ success: true, message: `Embedding stored with ID: ${embeddingId}` });
      }
      
      case "search-embeddings": {
        const searchQuery = formData.get("searchQuery") as string;
        const objectType = formData.get("searchObjectType") as string || undefined;
        
        // In a real application, you would generate embeddings using an AI model
        // For this demo, we'll use a simple mock embedding
        const mockEmbedding = Array.from({ length: 1536 }, () => Math.random() * 2 - 1);
        
        const results = await searchEmbeddings(mockEmbedding, objectType, 5);
        
        // Log the action
        await supabase.rpc('log_augment_action', {
          action: 'search_embeddings',
          user_id: user.id,
          details: { searchQuery, objectType, resultCount: results.length }
        });
        
        return json({ success: true, results });
      }
      
      case "sync-data": {
        const tableName = formData.get("tableName") as string;
        const limit = parseInt(formData.get("limit") as string || "100", 10);
        
        const count = await synchronizeData(tableName, limit);
        
        // Log the action
        await supabase.rpc('log_augment_action', {
          action: 'sync_data',
          user_id: user.id,
          details: { tableName, count }
        });
        
        return json({ success: true, message: `Synchronized ${count} records from ${tableName}` });
      }
      
      default:
        return json({ success: false, message: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error in action:", error);
    return json({ 
      success: false, 
      message: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}

export default function AugmentSupabaseDemo() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  const [activeTab, setActiveTab] = useState("embeddings");
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Augment-Supabase Integration Demo</h1>
      
      {actionData && (
        <div className={`p-4 mb-6 rounded ${actionData.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          <p>{actionData.message}</p>
          {actionData.results && (
            <pre className="mt-2 bg-white p-2 rounded overflow-auto max-h-60">
              {JSON.stringify(actionData.results, null, 2)}
            </pre>
          )}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Store Embedding</CardTitle>
            <CardDescription>Store a vector embedding for semantic search</CardDescription>
          </CardHeader>
          <CardContent>
            <Form method="post">
              <input type="hidden" name="action" value="store-embedding" />
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="objectId">Object ID</Label>
                  <Input id="objectId" name="objectId" required />
                </div>
                
                <div>
                  <Label htmlFor="objectType">Object Type</Label>
                  <Input id="objectType" name="objectType" required />
                </div>
                
                <div>
                  <Label htmlFor="content">Content</Label>
                  <Textarea id="content" name="content" rows={4} required />
                </div>
                
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Storing..." : "Store Embedding"}
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Search Embeddings</CardTitle>
            <CardDescription>Search for similar content using vector embeddings</CardDescription>
          </CardHeader>
          <CardContent>
            <Form method="post">
              <input type="hidden" name="action" value="search-embeddings" />
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="searchQuery">Search Query</Label>
                  <Input id="searchQuery" name="searchQuery" required />
                </div>
                
                <div>
                  <Label htmlFor="searchObjectType">Object Type (Optional)</Label>
                  <Input id="searchObjectType" name="searchObjectType" />
                </div>
                
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Searching..." : "Search"}
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Synchronize Data</CardTitle>
            <CardDescription>Sync data between Prisma and Supabase</CardDescription>
          </CardHeader>
          <CardContent>
            <Form method="post">
              <input type="hidden" name="action" value="sync-data" />
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="tableName">Table Name</Label>
                  <Input id="tableName" name="tableName" required />
                </div>
                
                <div>
                  <Label htmlFor="limit">Limit</Label>
                  <Input id="limit" name="limit" type="number" defaultValue="100" />
                </div>
                
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Synchronizing..." : "Synchronize"}
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="embeddings">Embeddings</TabsTrigger>
          <TabsTrigger value="files">Files</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
        </TabsList>
        
        <TabsContent value="embeddings">
          <Card>
            <CardHeader>
              <CardTitle>Vector Embeddings</CardTitle>
              <CardDescription>Recent vector embeddings stored in Supabase</CardDescription>
            </CardHeader>
            <CardContent>
              {loaderData.embeddings.error ? (
                <p className="text-red-600">{loaderData.embeddings.error}</p>
              ) : loaderData.embeddings.data.length === 0 ? (
                <p className="text-gray-500">No embeddings found</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="p-2 text-left">ID</th>
                        <th className="p-2 text-left">Object ID</th>
                        <th className="p-2 text-left">Object Type</th>
                        <th className="p-2 text-left">Content</th>
                        <th className="p-2 text-left">Created At</th>
                      </tr>
                    </thead>
                    <tbody>
                      {loaderData.embeddings.data.map((embedding) => (
                        <tr key={embedding.id} className="border-t">
                          <td className="p-2">{embedding.id}</td>
                          <td className="p-2">{embedding.object_id}</td>
                          <td className="p-2">{embedding.object_type}</td>
                          <td className="p-2">{embedding.content.substring(0, 50)}...</td>
                          <td className="p-2">{new Date(embedding.created_at).toLocaleString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="files">
          <Card>
            <CardHeader>
              <CardTitle>File Metadata</CardTitle>
              <CardDescription>Files stored in Supabase Storage</CardDescription>
            </CardHeader>
            <CardContent>
              {loaderData.files.error ? (
                <p className="text-red-600">{loaderData.files.error}</p>
              ) : loaderData.files.data.length === 0 ? (
                <p className="text-gray-500">No files found</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="p-2 text-left">ID</th>
                        <th className="p-2 text-left">Bucket</th>
                        <th className="p-2 text-left">Path</th>
                        <th className="p-2 text-left">Size</th>
                        <th className="p-2 text-left">MIME Type</th>
                        <th className="p-2 text-left">Created At</th>
                      </tr>
                    </thead>
                    <tbody>
                      {loaderData.files.data.map((file) => (
                        <tr key={file.id} className="border-t">
                          <td className="p-2">{file.id}</td>
                          <td className="p-2">{file.bucket}</td>
                          <td className="p-2">{file.path}</td>
                          <td className="p-2">{file.size} bytes</td>
                          <td className="p-2">{file.mime_type}</td>
                          <td className="p-2">{new Date(file.created_at).toLocaleString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="logs">
          <Card>
            <CardHeader>
              <CardTitle>Augment Logs</CardTitle>
              <CardDescription>Recent actions performed with Augment</CardDescription>
            </CardHeader>
            <CardContent>
              {loaderData.logs.error ? (
                <p className="text-red-600">{loaderData.logs.error}</p>
              ) : loaderData.logs.data.length === 0 ? (
                <p className="text-gray-500">No logs found</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="p-2 text-left">ID</th>
                        <th className="p-2 text-left">Action</th>
                        <th className="p-2 text-left">User ID</th>
                        <th className="p-2 text-left">Details</th>
                        <th className="p-2 text-left">Created At</th>
                      </tr>
                    </thead>
                    <tbody>
                      {loaderData.logs.data.map((log) => (
                        <tr key={log.id} className="border-t">
                          <td className="p-2">{log.id}</td>
                          <td className="p-2">{log.action}</td>
                          <td className="p-2">{log.user_id}</td>
                          <td className="p-2">
                            <pre className="text-xs">{JSON.stringify(log.details, null, 2)}</pre>
                          </td>
                          <td className="p-2">{new Date(log.created_at).toLocaleString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
