import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { PlusIcon, ArrowPathIcon, TableCellsIcon, ArrowTrendingUpIcon, ArchiveBoxIcon, ClockIcon, TruckIcon } from "@heroicons/react/24/outline";
import { requireUserId } from "~/session.server";
import { prisma } from "~/db.server";
import { getInventoryAnalytics, getReorderList, getPartsUsageHistory, getInventoryAlerts } from "~/models/inventory.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Get query parameters
  const url = new URL(request.url);
  const tab = url.searchParams.get("tab") || "dashboard";
  
  // Fetch data based on selected tab
  let data: any = {};
  
  // Get all inventory locations
  const locations = await prisma.location.findMany({
    where: {
      isActive: true,
      type: "INVENTORY"
    },
    orderBy: {
      name: "asc"
    }
  });
  
  // Common data for all tabs
  data.locations = locations;
  
  // Tab-specific data
  switch (tab) {
    case "dashboard":
      data.analytics = await getInventoryAnalytics();
      data.alerts = await getInventoryAlerts();
      break;
      
    case "reorder":
      data.reorderList = await getReorderList();
      data.suppliers = await prisma.supplier.findMany({
        orderBy: {
          name: "asc"
        }
      });
      break;
      
    case "usage":
      const days = Number(url.searchParams.get("days") || "90");
      data.usageHistory = await getPartsUsageHistory(days);
      break;
      
    case "transfers":
      data.recentTransfers = await prisma.inventoryTransaction.findMany({
        where: {
          transactionType: "TRANSFER"
        },
        include: {
          part: true,
          location: true,
          transferToLocation: true,
          user: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          transactionDate: "desc"
        },
        take: 100
      });
      break;
  }
  
  return json({ tab, ...data });
}

export default function InventoryAdvanced() {
  const { tab, analytics, alerts, reorderList, usageHistory, recentTransfers, locations, suppliers } = 
    useLoaderData<typeof loader>();
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Advanced Inventory Management</h1>
        <div className="flex gap-2">
          <Link 
            to="/inventory"
            className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <ArchiveBoxIcon className="h-4 w-4" />
            Basic View
          </Link>
          <Link 
            to="/inventory/parts/new" 
            className="flex items-center gap-1 rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4" />
            Add Part
          </Link>
        </div>
      </div>
      
      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex -mb-px space-x-8">
          <Link
            to="/inventory/advanced?tab=dashboard"
            className={`${tab === 'dashboard' 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} 
              whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
          >
            Dashboard
          </Link>
          <Link
            to="/inventory/advanced?tab=reorder"
            className={`${tab === 'reorder' 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} 
              whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
          >
            Reorder Management
          </Link>
          <Link
            to="/inventory/advanced?tab=usage"
            className={`${tab === 'usage' 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} 
              whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
          >
            Usage Analysis
          </Link>
          <Link
            to="/inventory/advanced?tab=transfers"
            className={`${tab === 'transfers' 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} 
              whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
          >
            Transfers
          </Link>
        </nav>
      </div>
      
      {/* Tab Content */}
      <div className="mt-6">
        {tab === 'dashboard' && (
          <DashboardTab analytics={analytics} alerts={alerts} />
        )}
        
        {tab === 'reorder' && (
          <ReorderTab reorderList={reorderList} suppliers={suppliers} />
        )}
        
        {tab === 'usage' && (
          <UsageTab usageHistory={usageHistory} />
        )}
        
        {tab === 'transfers' && (
          <TransfersTab recentTransfers={recentTransfers} locations={locations} />
        )}
      </div>
    </div>
  );
}

function DashboardTab({ analytics, alerts }: { analytics: any, alerts: any }) {
  return (
    <div className="space-y-6">
      {/* Analytics Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArchiveBoxIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Total Inventory Value</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(analytics.totalValue)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowPathIcon className="h-6 w-6 text-red-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Low Stock Items</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {alerts.lowStockParts.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <Link to="/inventory/advanced?tab=reorder" className="font-medium text-blue-700 hover:text-blue-900">
                View reorder list
              </Link>
            </div>
          </div>
        </div>
        
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-yellow-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Parts Expiring Soon</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {alerts.expiringSoonParts.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <a href="#expiring" className="font-medium text-blue-700 hover:text-blue-900">
                View expiring items
              </a>
            </div>
          </div>
        </div>
        
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TruckIcon className="h-6 w-6 text-green-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Inventory Locations</dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {analytics.locationBreakdown.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Inventory by Category */}
      <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Inventory by Category</h3>
            <div className="mt-5 h-64 overflow-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="sticky top-0 z-10 bg-gray-50 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Category
                    </th>
                    <th scope="col" className="sticky top-0 z-10 bg-gray-50 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Items
                    </th>
                    <th scope="col" className="sticky top-0 z-10 bg-gray-50 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Value
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {analytics.categoryCounts.map((category: any) => (
                    <tr key={category.category || 'uncategorized'}>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                        {category.category || 'Uncategorized'}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {category._count.id}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(category._sum.currentValuePLN || 0)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Inventory by Location</h3>
            <div className="mt-5 h-64 overflow-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="sticky top-0 z-10 bg-gray-50 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Location
                    </th>
                    <th scope="col" className="sticky top-0 z-10 bg-gray-50 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Items Quantity
                    </th>
                    <th scope="col" className="sticky top-0 z-10 bg-gray-50 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {analytics.locationBreakdown.map((location: any) => (
                    <tr key={location.locationId}>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                        {location.locationName}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {location.quantity}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        <Link 
                          to={`/inventory?location=${location.locationId}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          View Items
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      {/* Low Stock Alert */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Low Stock Alerts</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Current Stock
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Minimum Level
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Supplier
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {alerts.lowStockParts.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      No low stock alerts at this time.
                    </td>
                  </tr>
                ) : alerts.lowStockParts.slice(0, 5).map((part: any) => (
                  <tr key={part.id} className="bg-red-50">
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                        {part.name}
                      </Link>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-red-700 font-semibold">
                      {part.currentStock} {part.unitOfMeasure}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {part.minimumStock} {part.unitOfMeasure}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {part.supplier?.name || 'No supplier'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <Link 
                        to={`/inventory/parts/${part.id}/restock`}
                        className="text-blue-600 hover:text-blue-900 mr-4"
                      >
                        Restock
                      </Link>
                      <Link 
                        to={`/inventory/purchase-orders/new?partId=${part.id}`}
                        className="text-green-600 hover:text-green-900"
                      >
                        Order
                      </Link>
                    </td>
                  </tr>
                ))}
                {alerts.lowStockParts.length > 5 && (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      <Link to="/inventory/advanced?tab=reorder" className="text-blue-600 hover:underline">
                        View all {alerts.lowStockParts.length} low stock items
                      </Link>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      {/* Expiring Parts */}
      <div id="expiring" className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Parts Expiring Soon</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Current Stock
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Expiration Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Days Left
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {alerts.expiringSoonParts.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      No parts expiring soon.
                    </td>
                  </tr>
                ) : alerts.expiringSoonParts.map((part: any) => {
                  const daysLeft = Math.ceil((new Date(part.expirationDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24));
                  const rowClass = daysLeft < 7 ? 'bg-red-50' : daysLeft < 14 ? 'bg-yellow-50' : 'bg-white';
                  
                  return (
                    <tr key={part.id} className={rowClass}>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                        <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                          {part.name}
                        </Link>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {part.currentStock} {part.unitOfMeasure}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {new Date(part.expirationDate).toLocaleDateString()}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                        <span className={`${daysLeft < 7 ? 'text-red-700' : daysLeft < 14 ? 'text-yellow-700' : 'text-green-700'}`}>
                          {daysLeft} days
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        <Link 
                          to={`/inventory/parts/${part.id}/edit`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Manage
                        </Link>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

function ReorderTab({ reorderList, suppliers }: { reorderList: any[], suppliers: any[] }) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">Reorder Management</h2>
        <div className="flex space-x-3">
          <Link 
            to="/inventory/purchase-orders/new"
            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
          >
            Create Purchase Order
          </Link>
          <button 
            type="button"
            className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
          >
            Export Reorder List
          </button>
        </div>
      </div>
      
      {/* Group by supplier */}
      <div className="space-y-8">
        {suppliers.map(supplier => {
          const supplierParts = reorderList.filter(part => part.supplierId === supplier.id);
          if (supplierParts.length === 0) return null;
          
          return (
            <div key={supplier.id} className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
              <div className="bg-gray-50 px-4 py-5 sm:px-6">
                <h3 className="text-lg font-medium leading-6 text-gray-900">{supplier.name}</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {supplierParts.length} part{supplierParts.length !== 1 ? 's' : ''} to reorder
                </p>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Part Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Part Number
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Current Stock
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Reorder Point
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Suggested Order
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Last Order Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {supplierParts.map((part) => {
                      const suggestedOrder = Math.max(part.reorderQuantity || (part.reorderPoint * 2 - part.currentStock), 1);
                      
                      return (
                        <tr key={part.id} className={part.currentStock <= part.minimumStock ? 'bg-red-50' : ''}>
                          <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                            <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                              {part.name}
                            </Link>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {part.partNumber || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <span 
                                className={`mr-2 h-2 w-2 rounded-full ${part.currentStock <= part.minimumStock ? 'bg-red-500' : 'bg-yellow-500'}`}
                              ></span>
                              {part.currentStock} {part.unitOfMeasure}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {part.reorderPoint} {part.unitOfMeasure}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {suggestedOrder} {part.unitOfMeasure}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {part.lastOrderDate ? new Date(part.lastOrderDate).toLocaleDateString() : 'Never'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            <Link 
                              to={`/inventory/purchase-orders/new?partId=${part.id}&quantity=${suggestedOrder}`}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Order Now
                            </Link>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          );
        })}
        
        {/* Parts with no supplier */}
        {
          (() => {
            const noSupplierParts = reorderList.filter(part => !part.supplierId);
            if (noSupplierParts.length === 0) return null;
            
            return (
              <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
                <div className="bg-gray-50 px-4 py-5 sm:px-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">No Supplier Assigned</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {noSupplierParts.length} part{noSupplierParts.length !== 1 ? 's' : ''} to reorder without supplier
                  </p>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Part Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Part Number
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Current Stock
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Reorder Point
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Suggested Order
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {noSupplierParts.map((part) => {
                        const suggestedOrder = Math.max(part.reorderQuantity || (part.reorderPoint * 2 - part.currentStock), 1);
                        
                        return (
                          <tr key={part.id} className={part.currentStock <= part.minimumStock ? 'bg-red-50' : ''}>
                            <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                              <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                                {part.name}
                              </Link>
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {part.partNumber || '-'}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              <div className="flex items-center">
                                <span 
                                  className={`mr-2 h-2 w-2 rounded-full ${part.currentStock <= part.minimumStock ? 'bg-red-500' : 'bg-yellow-500'}`}
                                ></span>
                                {part.currentStock} {part.unitOfMeasure}
                              </div>
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {part.reorderPoint} {part.unitOfMeasure}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {suggestedOrder} {part.unitOfMeasure}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              <Link 
                                to={`/inventory/parts/${part.id}/edit`}
                                className="text-blue-600 hover:text-blue-900 mr-3"
                              >
                                Assign Supplier
                              </Link>
                              <Link 
                                to={`/inventory/parts/${part.id}/restock`}
                                className="text-green-600 hover:text-green-900"
                              >
                                Restock
                              </Link>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            );
          })()
        }
        
        {reorderList.length === 0 && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="h-5 w-5 text-green-400" aria-hidden="true">✓</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">All stocked up!</h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>There are currently no parts that need reordering.</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function UsageTab({ usageHistory }: { usageHistory: any[] }) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">Usage Analysis</h2>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500">Time Period:</span>
          <div className="flex space-x-2">
            <Link 
              to="/inventory/advanced?tab=usage&days=30"
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              30 Days
            </Link>
            <Link 
              to="/inventory/advanced?tab=usage&days=90"
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              90 Days
            </Link>
            <Link 
              to="/inventory/advanced?tab=usage&days=365"
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              1 Year
            </Link>
          </div>
        </div>
      </div>
      
      {/* Top used parts */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Most Used Parts</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Total Usage
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Current Stock
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Stock/Usage Ratio
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Last Used
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Frequency
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {usageHistory.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      No usage data available for the selected period.
                    </td>
                  </tr>
                ) : usageHistory.slice(0, 10).map((item) => {
                  const stockUsageRatio = item.part.currentStock / item.totalUsage;
                  let statusClass = '';
                  
                  if (stockUsageRatio < 0.5) {
                    statusClass = 'text-red-600 font-medium';
                  } else if (stockUsageRatio < 1) {
                    statusClass = 'text-yellow-600 font-medium';
                  } else {
                    statusClass = 'text-green-600 font-medium';
                  }
                  
                  // Calculate frequency (average days between uses)
                  let frequency = 'N/A';
                  if (item.transactions.length > 1) {
                    const firstDate = new Date(item.transactions[item.transactions.length - 1].transactionDate);
                    const lastDate = new Date(item.transactions[0].transactionDate);
                    const daysBetween = Math.floor((lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24));
                    const usageCount = item.transactions.length;
                    
                    if (daysBetween > 0 && usageCount > 1) {
                      const avgDays = (daysBetween / (usageCount - 1)).toFixed(1);
                      frequency = `${avgDays} days`;
                    }
                  }
                  
                  return (
                    <tr key={item.part.id}>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                        <Link to={`/inventory/parts/${item.part.id}`} className="text-blue-600 hover:underline">
                          {item.part.name}
                        </Link>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {item.totalUsage} {item.part.unitOfMeasure}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {item.part.currentStock} {item.part.unitOfMeasure}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        <span className={statusClass}>
                          {stockUsageRatio.toFixed(1)}x
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {new Date(item.transactions[0].transactionDate).toLocaleDateString()}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {frequency}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      {/* Usage Trends */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Usage by Category</h3>
          <div className="mt-5">
            {/* Placeholder for chart */}
            <div className="h-64 rounded-lg border border-gray-200 bg-gray-50 flex items-center justify-center">
              <p className="text-gray-500">Chart visualization would go here - showing usage trends by category</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Recent Usage Transactions */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Recent Usage Transactions</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Reference
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Reason
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {usageHistory.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      No usage data available for the selected period.
                    </td>
                  </tr>
                ) : (
                  // Flatten all transactions and sort by date
                  usageHistory
                    .flatMap(item => item.transactions)
                    .sort((a, b) => new Date(b.transactionDate).getTime() - new Date(a.transactionDate).getTime())
                    .slice(0, 20)
                    .map((transaction) => (
                      <tr key={transaction.id}>
                        <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                          {new Date(transaction.transactionDate).toLocaleDateString()}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                          <Link to={`/inventory/parts/${transaction.partId}`} className="text-blue-600 hover:underline">
                            {transaction.part.name}
                          </Link>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                          {Math.abs(transaction.quantity)} {transaction.part.unitOfMeasure}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                          {transaction.referenceType && transaction.referenceId ? (
                            <Link 
                              to={`/${transaction.referenceType.toLowerCase()}s/${transaction.referenceId}`}
                              className="text-blue-600 hover:underline"
                            >
                              {transaction.referenceType.replace('_', ' ')} #{transaction.referenceId.substring(0, 8)}
                            </Link>
                          ) : 'N/A'}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                          {transaction.reason || 'No reason specified'}
                        </td>
                      </tr>
                    ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

function TransfersTab({ recentTransfers, locations }: { recentTransfers: any[], locations: any[] }) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium text-gray-900">Stock Transfers</h2>
        <button 
          type="button"
          className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
        >
          New Transfer
        </button>
      </div>
      
      {/* Transfer Form */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Create Transfer</h3>
          <form className="mt-5 grid grid-cols-1 gap-6 sm:grid-cols-6">
            <div className="sm:col-span-3">
              <label htmlFor="part" className="block text-sm font-medium text-gray-700">
                Part
              </label>
              <div className="mt-1">
                <select
                  id="part"
                  name="part"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                >
                  <option value="">Select a part</option>
                  {/* Parts would be loaded dynamically */}
                </select>
              </div>
            </div>
            
            <div className="sm:col-span-1">
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                Quantity
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  name="quantity"
                  id="quantity"
                  min="1"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                />
              </div>
            </div>
            
            <div className="sm:col-span-3">
              <label htmlFor="from_location" className="block text-sm font-medium text-gray-700">
                From Location
              </label>
              <div className="mt-1">
                <select
                  id="from_location"
                  name="from_location"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                >
                  <option value="">Select source location</option>
                  {locations.map(location => (
                    <option key={location.id} value={location.id}>{location.name}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="sm:col-span-3">
              <label htmlFor="to_location" className="block text-sm font-medium text-gray-700">
                To Location
              </label>
              <div className="mt-1">
                <select
                  id="to_location"
                  name="to_location"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                >
                  <option value="">Select destination location</option>
                  {locations.map(location => (
                    <option key={location.id} value={location.id}>{location.name}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="sm:col-span-6">
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700">
                Reason for Transfer
              </label>
              <div className="mt-1">
                <textarea
                  id="reason"
                  name="reason"
                  rows={2}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                ></textarea>
              </div>
            </div>
            
            <div className="sm:col-span-6 flex justify-end">
              <button
                type="submit"
                className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Transfer Stock
              </button>
            </div>
          </form>
        </div>
      </div>
      
      {/* Recent Transfers */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Recent Transfers</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    From
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    To
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Performed By
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Reason
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {recentTransfers.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                      No transfers found.
                    </td>
                  </tr>
                ) : recentTransfers.map((transfer) => (
                  <tr key={transfer.id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {new Date(transfer.transactionDate).toLocaleDateString()}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      <Link to={`/inventory/parts/${transfer.partId}`} className="text-blue-600 hover:underline">
                        {transfer.part.name}
                      </Link>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {Math.abs(transfer.quantity)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transfer.location?.name || 'Unknown'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transfer.transferToLocation?.name || 'Unknown'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transfer.user?.name || 'System'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transfer.reason || 'No reason specified'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      {/* Inventory Map */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Inventory Location Map</h3>
          <div className="mt-5">
            {/* Placeholder for location map */}
            <div className="h-64 rounded-lg border border-gray-200 bg-gray-50 flex items-center justify-center">
              <p className="text-gray-500">Location map visualization would go here - showing inventory across locations</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}