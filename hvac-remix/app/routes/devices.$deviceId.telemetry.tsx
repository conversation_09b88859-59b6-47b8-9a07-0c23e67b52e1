import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useNavigation, useRevalidator, Form } from "@remix-run/react";
import { useState, useEffect } from "react";
import { requireUserId } from "~/session.server";
import { getDeviceById } from "~/services/device.service";
import { getDeviceTelemetry, recordDeviceTelemetry, getPredictiveMaintenance } from "~/services/predictive-maintenance.server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { DeviceTelemetryDashboard } from "~/components/organisms/device/DeviceTelemetryDashboard";
import { PredictiveMaintenancePanel } from "~/components/organisms/device/PredictiveMaintenancePanel";
import { TelemetryChart } from "~/components/organisms/TelemetryChart";
import { RefreshCw } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Label } from "~/components/ui/label";
import { Input } from "~/components/ui/input";

// Types
type TabValue = 'history' | 'charts' | 'record' | 'telemetry' | 'maintenance';
type MaintenanceStatus = 'optimal' | 'warning' | 'critical' | 'maintenance_required';

// Type guard for MaintenanceStatus
const isMaintenanceStatus = (status: string): status is MaintenanceStatus => {
  return ['optimal', 'warning', 'critical', 'maintenance_required'].includes(status);
};

interface TelemetryData {
  id: string;
  timestamp: string | Date;
  temperature: number | null;
  humidity: number | null;
  pressure: number | null;
  vibration: number | null;
  noise: number | null;
  powerUsage: number | null;
  runtime: number | null;
  cycles: number | null;
  errorCodes: string[] | null;
  deviceId: string;
  createdAt: string | Date;
  updatedAt: string | Date;
}

interface MaintenanceIssue {
  id: string;
  component: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  detectedAt: string | Date;
  estimatedRepairTime?: string;
  recommendedAction: string;
}

interface MaintenanceData {
  status: MaintenanceStatus;
  healthScore: number;
  nextMaintenance: string | Date | null;
  remainingUsefulLife: number;
  lastMaintenanceDate: string | Date | null;
  maintenanceInterval: number;
  issues: MaintenanceIssue[];
  lastUpdated: string | Date;
}

type DeviceType = NonNullable<Awaited<ReturnType<typeof getDeviceById>>['data']>;
type TelemetryType = NonNullable<Awaited<ReturnType<typeof getDeviceTelemetry>>['data']>;
type MaintenanceType = NonNullable<Awaited<ReturnType<typeof getPredictiveMaintenance>>['data']>;

interface LoaderData {
  device: DeviceType;
  telemetry: TelemetryType;
  maintenance: MaintenanceType;
  error: string | null;
}

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const deviceId = params.deviceId as string;
  
  // Get device details
  const deviceResponse = await getDeviceById(deviceId, userId);
  
  if (!deviceResponse.success || !deviceResponse.data) {
    throw new Response("Device not found", { status: 404 });
  }
  
  // Get telemetry data
  const telemetryResponse = await getDeviceTelemetry(deviceId, userId, {
    pageSize: 100, // Get more data for charts
    orderBy: "timestamp",
    orderDirection: "desc",
  });
  
  // Get predictive maintenance data
  const maintenanceResponse = await getPredictiveMaintenance(deviceId, userId);
  
  return json<LoaderData>({
    device: deviceResponse.data,
    telemetry: telemetryResponse.data || [],
    // Ensure maintenance data exists with proper typing
    maintenance: maintenanceResponse.data || {
      status: 'optimal' as const,
      healthScore: 85,
      nextMaintenance: null,
      remainingUsefulLife: 365,
      lastMaintenanceDate: null,
      maintenanceInterval: 180,
      issues: [],
      lastUpdated: new Date().toISOString(),
    },
    error: telemetryResponse.error || maintenanceResponse.error || null,
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const deviceId = params.deviceId as string;
  
  const formData = await request.formData();
  const timestamp = new Date();
  
  // Parse telemetry data from form
  const telemetryData = {
    timestamp,
    temperature: parseFloat(formData.get("temperature") as string) || null,
    humidity: parseFloat(formData.get("humidity") as string) || null,
    pressure: parseFloat(formData.get("pressure") as string) || null,
    vibration: parseFloat(formData.get("vibration") as string) || null,
    noise: parseFloat(formData.get("noise") as string) || null,
    powerUsage: parseFloat(formData.get("powerUsage") as string) || null,
    runtime: parseFloat(formData.get("runtime") as string) || null,
    cycles: parseInt(formData.get("cycles") as string) || null,
    errorCodes: formData.get("errorCodes") as string || null,
    customData: null, // Not implemented in the form yet
  };
  
  // Record telemetry data
  const response = await recordDeviceTelemetry(deviceId, telemetryData, userId);
  
  return json({
    success: response.success,
    error: response.error,
    telemetry: response.data,
  });
}

// Auto-refresh interval in milliseconds
const REFRESH_INTERVAL = 30000; // 30 seconds

interface ActionData {
  success?: boolean;
  error?: string;
  telemetry?: TelemetryData;
}

export default function DeviceTelemetryPage() {
  const { device, telemetry = [], maintenance, error } = useLoaderData<LoaderData>();
  
  if (!device) {
    return (
      <div className="p-8">
        <Alert variant="destructive">
          <AlertTitle>Device not found</AlertTitle>
          <AlertDescription>The requested device could not be found.</AlertDescription>
        </Alert>
      </div>
    );
  }
  const actionData = useActionData<ActionData>();
  const navigation = useNavigation();
  const revalidator = useRevalidator();
  const isSubmitting = navigation.state === "submitting";
  
  const [activeTab, setActiveTab] = useState<TabValue>("telemetry");
  const [lastRefreshed, setLastRefreshed] = useState(new Date());
  
  // Set up auto-refresh
  useEffect(() => {
    const interval = setInterval(() => {
      revalidator.revalidate();
      setLastRefreshed(new Date());
    }, REFRESH_INTERVAL);
    
    return () => clearInterval(interval);
  }, [revalidator]);
  
  // Prepare data for charts
  const chartData = (telemetry || []).map(t => ({
    id: t.id,
    timestamp: t.timestamp,
    temperature: t.temperature,
    humidity: t.humidity,
    pressure: t.pressure,
    vibration: t.vibration,
    noise: t.noise,
    powerUsage: t.powerUsage,
    runtime: t.runtime,
    cycles: t.cycles,
    errorCodes: t.errorCodes || [],
    deviceId: t.deviceId,
    createdAt: t.createdAt,
    updatedAt: t.updatedAt,
  }));
  
  // Handle maintenance actions
  const handleScheduleMaintenance = () => {
    // TODO: Implement maintenance scheduling
    alert('Scheduling maintenance...');
  };
  
  const handleAcknowledgeIssue = (issueId: string) => {
    // TODO: Implement issue acknowledgment
    alert(`Acknowledging issue ${issueId}...`);
  };
  
  // Ensure status is a valid MaintenanceStatus
  const safeStatus = isMaintenanceStatus(maintenance.status) 
    ? maintenance.status 
    : 'optimal';
  
  return (
    <div className="container py-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{device.name}</h1>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>{device.model} {device.manufacturer ? `• ${device.manufacturer}` : ""}</span>
            <span>•</span>
            <span>Serial: {device.serialNumber || 'N/A'}</span>
            <span>•</span>
            <div className="flex items-center">
              <span className="relative flex h-2 w-2 mr-1">
                <span className={`animate-ping absolute inline-flex h-full w-full rounded-full ${
                  maintenance.status === 'optimal' ? 'bg-green-400' : 
                  maintenance.status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'
                } opacity-75`}></span>
                <span className={`relative inline-flex rounded-full h-2 w-2 ${
                  maintenance.status === 'optimal' ? 'bg-green-500' : 
                  maintenance.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></span>
              </span>
              <span className="capitalize">{maintenance.status.replace('_', ' ')}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-sm text-gray-500">
            Last updated: {new Date(lastRefreshed).toLocaleTimeString()}
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => {
              revalidator.revalidate();
              setLastRefreshed(new Date());
            }}
            disabled={revalidator.state === "loading"}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${revalidator.state === "loading" ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertDescription>Error loading telemetry data: {error}</AlertDescription>
        </Alert>
      )}
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="telemetry">Telemetry</TabsTrigger>
          <TabsTrigger value="maintenance">Predictive Maintenance</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="telemetry" className="space-y-6">
          <DeviceTelemetryDashboard 
            deviceId={device.id}
            initialData={{
              telemetry: chartData,
              healthScore: maintenance.healthScore,
              status: maintenance.status,
              nextMaintenance: maintenance.nextMaintenance,
              lastUpdated: maintenance.lastUpdated
            }}
          />
        </TabsContent>
        
        <TabsContent value="maintenance" className="space-y-6">
          <PredictiveMaintenancePanel 
            deviceId={device.id}
            status={safeStatus}
            healthScore={maintenance.healthScore}
            nextMaintenance={maintenance.nextMaintenance}
            remainingUsefulLife={maintenance.remainingUsefulLife}
            lastMaintenanceDate={maintenance.lastMaintenanceDate}
            maintenanceInterval={maintenance.maintenanceInterval}
            issues={maintenance.issues}
            lastUpdated={maintenance.lastUpdated}
            onScheduleMaintenance={handleScheduleMaintenance}
            onAcknowledgeIssue={handleAcknowledgeIssue}
          />
        </TabsContent>
        
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Telemetry History</CardTitle>
              <CardDescription>Detailed historical data for this device</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-96">
                {chartData.length > 0 ? (
                  <div className="h-full w-full overflow-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Timestamp
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Temperature
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Pressure
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Vibration
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Power
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {chartData.map((data, index) => (
                          <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(data.timestamp).toLocaleString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {data.temperature !== undefined ? `${data.temperature}°C` : 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {data.pressure !== undefined ? `${data.pressure} kPa` : 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {data.vibration !== undefined ? `${data.vibration} mm/s²` : 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {data.powerUsage !== undefined ? `${data.powerUsage} kW` : 'N/A'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    No telemetry data available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <div className="mt-6">
        <Button asChild variant="outline">
          <a href={`/devices/${device.id}`} className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Back to Device
          </a>
        </Button>
      </div>
      
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {actionData?.success && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>Telemetry data recorded successfully.</AlertDescription>
        </Alert>
      )}
      
      {actionData && actionData.error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{actionData.error}</AlertDescription>
        </Alert>
      )}
      
      <Tabs 
        value={activeTab} 
        onValueChange={(value) => {
          if (['history', 'charts', 'record', 'telemetry', 'maintenance'].includes(value)) {
            setActiveTab(value as TabValue);
          }
        }} 
        className="mb-6"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="history" onClick={() => setActiveTab('history')}>History</TabsTrigger>
          <TabsTrigger value="charts" onClick={() => setActiveTab('charts')}>Charts</TabsTrigger>
          <TabsTrigger value="record" onClick={() => setActiveTab('record')}>Record Data</TabsTrigger>
        </TabsList>
        
        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Telemetry History</CardTitle>
              <CardDescription>Recent telemetry data for this device</CardDescription>
            </CardHeader>
            <CardContent>
              {telemetry.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>Temp</TableHead>
                        <TableHead>Humidity</TableHead>
                        <TableHead>Pressure</TableHead>
                        <TableHead>Vibration</TableHead>
                        <TableHead>Noise</TableHead>
                        <TableHead>Power</TableHead>
                        <TableHead>Runtime</TableHead>
                        <TableHead>Cycles</TableHead>
                        <TableHead>Errors</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {telemetry.map((t) => (
                        <TableRow key={t.id}>
                          <TableCell>{new Date(t.timestamp).toLocaleString()}</TableCell>
                          <TableCell>{t.temperature !== null ? `${t.temperature}°C` : "-"}</TableCell>
                          <TableCell>{t.humidity !== null ? `${t.humidity}%` : "-"}</TableCell>
                          <TableCell>{t.pressure !== null ? `${t.pressure} kPa` : "-"}</TableCell>
                          <TableCell>{t.vibration !== null ? t.vibration : "-"}</TableCell>
                          <TableCell>{t.noise !== null ? t.noise : "-"}</TableCell>
                          <TableCell>{t.powerUsage !== null ? `${t.powerUsage} kW` : "-"}</TableCell>
                          <TableCell>{t.runtime !== null ? `${t.runtime} h` : "-"}</TableCell>
                          <TableCell>{t.cycles !== null ? t.cycles : "-"}</TableCell>
                          <TableCell>{t.errorCodes || "-"}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p className="text-center py-4 text-gray-500">No telemetry data available.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="charts" className="mt-6">
          <TelemetryChart 
            telemetry={chartData.filter(t => t.timestamp && (
              t.temperature !== null || 
              t.humidity !== null || 
              t.pressure !== null || 
              t.vibration !== null ||
              t.powerUsage !== null
            ))}
            title="Device Telemetry"
            description="Real-time sensor data from the device"
          />
        </TabsContent>
        
        <TabsContent value="record" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Record Telemetry Data</CardTitle>
              <CardDescription>Manually record telemetry data for this device</CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="temperature">Temperature (°C)</Label>
                    <Input id="temperature" name="temperature" type="number" step="0.1" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="humidity">Humidity (%)</Label>
                    <Input id="humidity" name="humidity" type="number" step="0.1" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="pressure">Pressure (kPa)</Label>
                    <Input id="pressure" name="pressure" type="number" step="0.1" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="vibration">Vibration</Label>
                    <Input id="vibration" name="vibration" type="number" step="0.01" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="noise">Noise</Label>
                    <Input id="noise" name="noise" type="number" step="0.1" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="powerUsage">Power Usage (kW)</Label>
                    <Input id="powerUsage" name="powerUsage" type="number" step="0.01" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="runtime">Runtime (hours)</Label>
                    <Input id="runtime" name="runtime" type="number" step="0.1" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="cycles">Cycles</Label>
                    <Input id="cycles" name="cycles" type="number" />
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="errorCodes">Error Codes</Label>
                    <Input id="errorCodes" name="errorCodes" />
                  </div>
                </div>
                
                <div className="mt-6">
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Recording..." : "Record Telemetry"}
                  </Button>
                </div>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
