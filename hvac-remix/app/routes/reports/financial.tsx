import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { ArrowLeftIcon, DownloadIcon, CalendarIcon } from "@heroicons/react/24/outline";
import { Link } from "@remix-run/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Button } from "~/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { requireUserId } from "~/session.server";
import { prisma } from "~/db.server";
import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hart, ResponsiveContainer, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Line, Pie, Cell } from "recharts";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);

  // Get URL parameters
  const url = new URL(request.url);
  const period = url.searchParams.get("period") || "month";

  // Calculate date ranges
  const now = new Date();
  let startDate = new Date();

  if (period === "month") {
    startDate.setMonth(now.getMonth() - 1);
  } else if (period === "quarter") {
    startDate.setMonth(now.getMonth() - 3);
  } else if (period === "year") {
    startDate.setFullYear(now.getFullYear() - 1);
  }

  // Get service orders with payments
  const serviceOrders = await prisma.serviceOrder.findMany({
    where: {
      userId,
      createdAt: {
        gte: startDate,
      },
    },
    include: {
      payments: true,
      customer: true,
      serviceType: true,
    },
  });

  // Get invoices
  const invoices = await prisma.invoice.findMany({
    where: {
      userId,
      createdAt: {
        gte: startDate,
      },
    },
    include: {
      customer: true,
      items: true,
    },
  });

  // Calculate revenue by service type
  const revenueByService: Record<string, { revenue: number; count: number }> = {};

  serviceOrders.forEach((order) => {
    const serviceType = order.serviceType?.name || "Other";
    const revenue = order.payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);

    if (!revenueByService[serviceType]) {
      revenueByService[serviceType] = { revenue: 0, count: 0 };
    }

    revenueByService[serviceType].revenue += revenue;
    revenueByService[serviceType].count += 1;
  });

  const revenueByServiceData = Object.entries(revenueByService).map(([serviceType, data]) => ({
    serviceType,
    revenue: data.revenue,
    count: data.count,
    averageValue: data.count > 0 ? data.revenue / data.count : 0,
    percentage: 0, // Will calculate below
  }));

  const totalRevenue = revenueByServiceData.reduce((sum, item) => sum + item.revenue, 0);

  // Calculate percentages
  revenueByServiceData.forEach((item) => {
    item.percentage = totalRevenue > 0 ? (item.revenue / totalRevenue) * 100 : 0;
  });

  // Sort by revenue (highest first)
  revenueByServiceData.sort((a, b) => b.revenue - a.revenue);

  // Calculate customer profitability
  const customerProfitability: Record<string, {
    customerId: string;
    customerName: string;
    totalRevenue: number;
    totalOrders: number;
    lastServiceDate: Date | null;
  }> = {};

  serviceOrders.forEach((order) => {
    const customerId = order.customerId;
    const customerName = order.customer?.name || "Unknown";
    const revenue = order.payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);

    if (!customerProfitability[customerId]) {
      customerProfitability[customerId] = {
        customerId,
        customerName,
        totalRevenue: 0,
        totalOrders: 0,
        lastServiceDate: null
      };
    }

    customerProfitability[customerId].totalRevenue += revenue;
    customerProfitability[customerId].totalOrders += 1;

    // Update last service date if newer
    const orderDate = new Date(order.createdAt);
    if (!customerProfitability[customerId].lastServiceDate ||
        orderDate > customerProfitability[customerId].lastServiceDate) {
      customerProfitability[customerId].lastServiceDate = orderDate;
    }
  });

  const customerProfitabilityData = Object.values(customerProfitability)
    .map(customer => ({
      ...customer,
      averageOrderValue: customer.totalOrders > 0
        ? customer.totalRevenue / customer.totalOrders
        : 0
    }))
    .sort((a, b) => b.totalRevenue - a.totalRevenue)
    .slice(0, 10); // Top 10 customers

  // Calculate monthly financials
  const monthlyFinancials: Record<string, {
    month: string;
    revenue: number;
    costs: number;
    profit: number;
    margin: number;
  }> = {};

  // Process service orders for monthly revenue
  serviceOrders.forEach((order) => {
    const date = new Date(order.createdAt);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    const monthLabel = date.toLocaleString('default', { month: 'short', year: 'numeric' });
    const revenue = order.payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);

    if (!monthlyFinancials[monthKey]) {
      monthlyFinancials[monthKey] = {
        month: monthLabel,
        revenue: 0,
        costs: 0,
        profit: 0,
        margin: 0
      };
    }

    monthlyFinancials[monthKey].revenue += revenue;
    // Estimate costs as 60% of revenue for this example
    monthlyFinancials[monthKey].costs += revenue * 0.6;
  });

  // Calculate profit and margin
  Object.values(monthlyFinancials).forEach(month => {
    month.profit = month.revenue - month.costs;
    month.margin = month.revenue > 0 ? (month.profit / month.revenue) * 100 : 0;
  });

  const monthlyFinancialsData = Object.values(monthlyFinancials)
    .sort((a, b) => a.month.localeCompare(b.month));

  // Calculate cost analysis
  const totalCosts = monthlyFinancialsData.reduce((sum, month) => sum + month.costs, 0);
  const grossProfit = totalRevenue - totalCosts;
  const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

  // Estimate parts and labor costs
  const partsCosts = totalCosts * 0.4; // 40% of costs
  const laborCosts = totalCosts * 0.6; // 60% of costs

  const costAnalysis = {
    totalRevenue,
    totalCosts,
    grossProfit,
    profitMargin,
    partsCosts,
    laborCosts
  };

  return json({
    period,
    revenueByService: revenueByServiceData,
    customerProfitability: customerProfitabilityData,
    monthlyFinancials: monthlyFinancialsData,
    costAnalysis
  });
};

// Color palette for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#8DD1E1'];

export default function FinancialReportsPage() {
  const {
    period,
    revenueByService,
    customerProfitability,
    monthlyFinancials,
    costAnalysis
  } = useLoaderData<typeof loader>();

  const [activePeriod, setActivePeriod] = useState(period);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link to="/reports" className="text-gray-500 hover:text-gray-700">
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold">Financial Reports</h1>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5 text-gray-500" />
            <Select
              value={activePeriod}
              onValueChange={(value) => {
                setActivePeriod(value);
                window.location.href = `/reports/financial?period=${value}`;
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="month">Last Month</SelectItem>
                <SelectItem value="quarter">Last Quarter</SelectItem>
                <SelectItem value="year">Last Year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button variant="outline" className="flex items-center gap-2">
            <DownloadIcon className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(costAnalysis.totalRevenue)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Gross Profit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(costAnalysis.grossProfit)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Profit Margin
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(costAnalysis.profitMargin)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Costs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(costAnalysis.totalCosts)}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="revenue">
        <TabsList className="mb-4">
          <TabsTrigger value="revenue">Revenue Analysis</TabsTrigger>
          <TabsTrigger value="customers">Customer Profitability</TabsTrigger>
          <TabsTrigger value="trends">Monthly Trends</TabsTrigger>
          <TabsTrigger value="costs">Cost Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue by Service Type</CardTitle>
              <CardDescription>
                Breakdown of revenue by service type for the selected period
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={revenueByService}
                    margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="serviceType"
                      angle={-45}
                      textAnchor="end"
                      height={70}
                    />
                    <YAxis
                      tickFormatter={(value) => formatCurrency(value).replace('.00', '')}
                    />
                    <Tooltip
                      formatter={(value: any) => formatCurrency(value)}
                      labelFormatter={(label) => `Service Type: ${label}`}
                    />
                    <Legend />
                    <Bar
                      dataKey="revenue"
                      name="Revenue"
                      fill="#0088FE"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">Revenue Distribution</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={revenueByService}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="revenue"
                          nameKey="serviceType"
                          label={({ serviceType, percentage }) => `${serviceType}: ${percentage.toFixed(1)}%`}
                        >
                          {revenueByService.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value: any) => formatCurrency(value)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>

                  <div>
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead>
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Service Type
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Revenue
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            %
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        {revenueByService.map((item, index) => (
                          <tr key={index}>
                            <td className="px-4 py-2 whitespace-nowrap text-sm">
                              <div className="flex items-center">
                                <div
                                  className="w-3 h-3 rounded-full mr-2"
                                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                                ></div>
                                {item.serviceType}
                              </div>
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm">
                              {formatCurrency(item.revenue)}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm">
                              {formatPercentage(item.percentage)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top 10 Customers by Revenue</CardTitle>
              <CardDescription>
                Your most valuable customers based on total revenue
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={customerProfitability}
                    margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      type="number"
                      tickFormatter={(value) => formatCurrency(value).replace('.00', '')}
                    />
                    <YAxis
                      type="category"
                      dataKey="customerName"
                      width={150}
                    />
                    <Tooltip
                      formatter={(value: any) => formatCurrency(value)}
                      labelFormatter={(label) => `Customer: ${label}`}
                    />
                    <Legend />
                    <Bar
                      dataKey="totalRevenue"
                      name="Total Revenue"
                      fill="#00C49F"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">Customer Details</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Customer
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Total Revenue
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Orders
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Avg. Order Value
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Last Service
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {customerProfitability.map((customer, index) => (
                        <tr key={index}>
                          <td className="px-4 py-2 whitespace-nowrap text-sm">
                            {customer.customerName}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm">
                            {formatCurrency(customer.totalRevenue)}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm">
                            {customer.totalOrders}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm">
                            {formatCurrency(customer.averageOrderValue)}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm">
                            {customer.lastServiceDate
                              ? new Date(customer.lastServiceDate).toLocaleDateString()
                              : 'N/A'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Financial Trends</CardTitle>
              <CardDescription>
                Revenue, costs, and profit trends over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyFinancials}
                    margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                      tickFormatter={(value) => formatCurrency(value).replace('.00', '')}
                    />
                    <Tooltip
                      formatter={(value: any) => formatCurrency(value)}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      name="Revenue"
                      stroke="#0088FE"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="costs"
                      name="Costs"
                      stroke="#FF8042"
                    />
                    <Line
                      type="monotone"
                      dataKey="profit"
                      name="Profit"
                      stroke="#00C49F"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">Profit Margin Trend</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={monthlyFinancials}
                      margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis
                        tickFormatter={(value) => `${value}%`}
                      />
                      <Tooltip
                        formatter={(value: any) => `${value.toFixed(1)}%`}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="margin"
                        name="Profit Margin %"
                        stroke="#8884D8"
                        activeDot={{ r: 8 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="costs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cost Analysis</CardTitle>
              <CardDescription>
                Breakdown of costs and profitability metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Cost Breakdown</h3>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={[
                            { name: 'Parts', value: costAnalysis.partsCosts },
                            { name: 'Labor', value: costAnalysis.laborCosts },
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          nameKey="name"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                        >
                          <Cell fill="#0088FE" />
                          <Cell fill="#00C49F" />
                        </Pie>
                        <Tooltip formatter={(value: any) => formatCurrency(value)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Profitability Summary</h3>
                  <div className="space-y-4">
                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 dark:text-gray-400">Total Revenue</div>
                      <div className="text-xl font-bold">{formatCurrency(costAnalysis.totalRevenue)}</div>
                    </div>

                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 dark:text-gray-400">Total Costs</div>
                      <div className="text-xl font-bold">{formatCurrency(costAnalysis.totalCosts)}</div>
                      <div className="mt-2 grid grid-cols-2 gap-2">
                        <div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">Parts</div>
                          <div className="text-sm">{formatCurrency(costAnalysis.partsCosts)}</div>
                        </div>
                        <div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">Labor</div>
                          <div className="text-sm">{formatCurrency(costAnalysis.laborCosts)}</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 dark:text-gray-400">Gross Profit</div>
                      <div className="text-xl font-bold">{formatCurrency(costAnalysis.grossProfit)}</div>
                    </div>

                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                      <div className="text-sm text-gray-500 dark:text-gray-400">Profit Margin</div>
                      <div className="text-xl font-bold">{formatPercentage(costAnalysis.profitMargin)}</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
