import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { getUser } from "~/session.server";
import { prisma } from "~/db.server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const user = await getUser(request);

  if (!user || user.role !== "CUSTOMER") {
    throw new Response("Unauthorized", { status: 401 });
  }

  const { invoiceId } = params;

  if (!invoiceId) {
    throw new Response("Invoice ID is required", { status: 400 });
  }

  // Get the invoice with related data
  const invoice = await prisma.invoice.findUnique({
    where: { id: invoiceId },
    include: {
      customer: true,
      serviceOrder: {
        include: {
          device: true,
        },
      },
      items: true,
    },
  });

  if (!invoice) {
    throw new Response("Invoice not found", { status: 404 });
  }

  // Check if the invoice belongs to the customer
  if (invoice.customer.id !== user.customerId) {
    throw new Response("Unauthorized", { status: 401 });
  }

  return json({ invoice });
};

export default function CustomerPortalInvoiceDetail() {
  const { invoice } = useLoaderData<typeof loader>();

  // Calculate subtotal
  const subtotal = invoice.items.reduce((sum, item) => sum + item.totalPrice, 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            Invoice #{invoice.invoiceNumber || invoice.id.substring(0, 8)}
          </h1>
          <p className="text-muted-foreground">
            {invoice.issueDate
              ? `Issued on ${new Date(invoice.issueDate).toLocaleDateString()}`
              : "No issue date"}
          </p>
        </div>
        <Badge
          variant={
            invoice.status === "PAID" ? "success" :
            invoice.status === "PENDING" ? "warning" :
            "destructive"
          }
          className="text-base py-1 px-3"
        >
          {invoice.status}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Invoice Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                <p>{invoice.status}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Issue Date</h3>
                <p>
                  {invoice.issueDate
                    ? new Date(invoice.issueDate).toLocaleDateString()
                    : "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Due Date</h3>
                <p>
                  {invoice.dueDate
                    ? new Date(invoice.dueDate).toLocaleDateString()
                    : "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Total Amount</h3>
                <p className="font-medium">${invoice.totalAmount?.toFixed(2) || "N/A"}</p>
              </div>
            </div>

            {invoice.notes && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
                <p className="mt-1">{invoice.notes}</p>
              </div>
            )}

            {invoice.serviceOrder && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Service Order</h3>
                <div className="mt-1">
                  <Link
                    to={`/customer-portal/service-orders/${invoice.serviceOrder.id}`}
                    className="text-primary hover:underline"
                  >
                    {invoice.serviceOrder.title}
                  </Link>
                  <p className="text-sm text-muted-foreground">
                    Status: {invoice.serviceOrder.status}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {invoice.serviceOrder?.device && (
          <Card>
            <CardHeader>
              <CardTitle>Device Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Device Name</h3>
                  <p>{invoice.serviceOrder.device.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Model</h3>
                  <p>{invoice.serviceOrder.device.model || "N/A"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Manufacturer</h3>
                  <p>{invoice.serviceOrder.device.manufacturer || "N/A"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Serial Number</h3>
                  <p>{invoice.serviceOrder.device.serialNumber || "N/A"}</p>
                </div>
              </div>

              <div className="flex justify-end">
                <Button asChild variant="outline" size="sm">
                  <Link to={`/customer-portal/devices/${invoice.serviceOrder.device.id}`}>
                    View Device Details
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Invoice Items</CardTitle>
        </CardHeader>
        <CardContent>
          {invoice.items.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="py-2 px-4 text-left">Description</th>
                    <th className="py-2 px-4 text-right">Quantity</th>
                    <th className="py-2 px-4 text-right">Unit Price</th>
                    <th className="py-2 px-4 text-right">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {invoice.items.map((item) => (
                    <tr key={item.id} className="border-b">
                      <td className="py-2 px-4">{item.description}</td>
                      <td className="py-2 px-4 text-right">{item.quantity}</td>
                      <td className="py-2 px-4 text-right">${item.unitPrice.toFixed(2)}</td>
                      <td className="py-2 px-4 text-right">${item.totalPrice.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan={3} className="py-2 px-4 text-right font-medium">Subtotal</td>
                    <td className="py-2 px-4 text-right">${subtotal.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="py-2 px-4 text-right font-medium">Tax</td>
                    <td className="py-2 px-4 text-right">${(invoice.taxAmount || 0).toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="py-2 px-4 text-right font-medium">Total</td>
                    <td className="py-2 px-4 text-right font-bold">${(invoice.totalAmount || 0).toFixed(2)}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <p className="text-muted-foreground">No items found for this invoice.</p>
          )}
        </CardContent>
      </Card>

      {invoice.originalDocumentUrl && (
        <Card>
          <CardHeader>
            <CardTitle>Original Invoice Document</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <iframe
                src={invoice.originalDocumentUrl}
                className="w-full h-96 border rounded-md"
                title="Original Invoice Document"
              />
            </div>
            <div className="mt-4 flex justify-center">
              <Button asChild variant="outline">
                <a
                  href={invoice.originalDocumentUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Download Original Invoice
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-between">
        <Button asChild variant="outline">
          <Link to="/customer-portal/invoices">Back to Invoices</Link>
        </Button>

        {invoice.status === "PENDING" && (
          <Button asChild>
            <Link to={`/customer-portal/invoices/${invoice.id}/pay`}>Pay Now</Link>
          </Button>
        )}
      </div>
    </div>
  );
}