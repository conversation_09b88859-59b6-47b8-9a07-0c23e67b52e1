import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderD<PERSON>, Link } from "@remix-run/react";
import { requireUserId } from "~/session.server";
import { getDeviceById } from "~/services/device.service";
import { getDevicePredictions } from "~/services/predictive-maintenance.server";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { MaintenanceForm } from "~/components/molecules/maintenance";
import { PredictionChart } from "~/components/molecules/device";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const deviceId = params.deviceId as string;

  // Get device details
  const deviceResponse = await getDeviceById(deviceId, userId);

  if (!deviceResponse.success || !deviceResponse.data) {
    throw new Response("Device not found", { status: 404 });
  }

  // Get prediction data
  const predictionsResponse = await getDevicePredictions(deviceId, userId, {
    pageSize: 100,
    orderBy: "predictionDate",
    orderDirection: "desc",
  });

  return json({
    device: deviceResponse.data,
    predictions: predictionsResponse.data || [],
    error: predictionsResponse.error,
  });
}

export default function DevicePredictionsPage() {
  const { device, predictions, error } = useLoaderData<typeof loader>();

  // Get the most recent prediction
  const latestPrediction = predictions.length > 0 ? predictions[0] : null;

  // Prepare data for failure probability chart
  const failureProbabilityData = [
    { name: "Failure Risk", value: latestPrediction ? latestPrediction.failureProbability * 100 : 0 },
    { name: "Safe", value: latestPrediction ? 100 - (latestPrediction.failureProbability * 100) : 100 },
  ];

  // Colors for the chart
  const COLORS = ["#ff0000", "#00c49f"];

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get status badge based on failure probability
  const getStatusBadge = (probability: number) => {
    if (probability < 0.3) {
      return <Badge className="bg-green-500">Low Risk</Badge>;
    } else if (probability < 0.7) {
      return <Badge className="bg-yellow-500">Medium Risk</Badge>;
    } else {
      return <Badge className="bg-red-500">High Risk</Badge>;
    }
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">{device.name} Maintenance Predictions</h1>
          <p className="text-gray-500">
            {device.model} {device.manufacturer ? `by ${device.manufacturer}` : ""}
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link to={`/devices/${device.id}/telemetry`}>View Telemetry</Link>
          </Button>
          <Button asChild variant="outline">
            <Link to={`/devices/${device.id}`}>Back to Device</Link>
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Latest Prediction</CardTitle>
            <CardDescription>
              {latestPrediction
                ? `Prediction from ${formatDate(latestPrediction.predictionDate)}`
                : "No predictions available"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {latestPrediction ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Failure Probability:</h3>
                  <div className="flex items-center gap-2">
                    <span className="text-xl font-bold">
                      {Math.round(latestPrediction.failureProbability * 100)}%
                    </span>
                    {getStatusBadge(latestPrediction.failureProbability)}
                  </div>
                </div>

                {latestPrediction.predictedFailureDate && (
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Predicted Failure Date:</h3>
                    <span className="text-xl font-bold">
                      {formatDate(latestPrediction.predictedFailureDate)}
                    </span>
                  </div>
                )}

                {latestPrediction.predictedComponent && (
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Predicted Component:</h3>
                    <span className="text-xl font-bold">
                      {latestPrediction.predictedComponent}
                    </span>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Confidence:</h3>
                  <span className="text-xl font-bold">
                    {Math.round(latestPrediction.confidence * 100)}%
                  </span>
                </div>

                {latestPrediction.recommendedAction && (
                  <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                    <h3 className="text-lg font-medium mb-2">Recommended Action:</h3>
                    <p>{latestPrediction.recommendedAction}</p>
                  </div>
                )}

                {latestPrediction.modelFeatures && (
                  <div className="mt-4">
                    <h3 className="text-lg font-medium mb-2">Key Indicators:</h3>
                    <ul className="list-disc pl-5">
                      {JSON.parse(latestPrediction.modelFeatures).map((feature: string, index: number) => (
                        <li key={index}>{feature}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-center py-4 text-gray-500">
                No prediction data available. Record telemetry data to generate predictions.
              </p>
            )}
          </CardContent>
          {latestPrediction && !latestPrediction.maintenancePerformed && (
            <CardFooter>
              <MaintenanceForm predictionId={latestPrediction.id} />
            </CardFooter>
          )}
        </Card>

        <PredictionChart
          prediction={latestPrediction}
          title="Failure Risk"
          description="Current risk assessment"
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Prediction History</CardTitle>
          <CardDescription>Historical maintenance predictions for this device</CardDescription>
        </CardHeader>
        <CardContent>
          {predictions.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Failure Probability</TableHead>
                    <TableHead>Predicted Failure</TableHead>
                    <TableHead>Component</TableHead>
                    <TableHead>Confidence</TableHead>
                    <TableHead>Maintenance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {predictions.map((prediction) => (
                    <TableRow key={prediction.id}>
                      <TableCell>{formatDate(prediction.predictionDate)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {Math.round(prediction.failureProbability * 100)}%
                          {getStatusBadge(prediction.failureProbability)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {prediction.predictedFailureDate
                          ? formatDate(prediction.predictedFailureDate)
                          : "-"}
                      </TableCell>
                      <TableCell>{prediction.predictedComponent || "-"}</TableCell>
                      <TableCell>{Math.round(prediction.confidence * 100)}%</TableCell>
                      <TableCell>
                        {prediction.maintenancePerformed ? (
                          <Badge className="bg-green-500">Performed</Badge>
                        ) : (
                          <Badge variant="outline">Pending</Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <p className="text-center py-4 text-gray-500">No prediction history available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
