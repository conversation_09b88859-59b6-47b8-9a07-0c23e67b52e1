import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { prisma } from "~/db.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);

  // Get suppliers for dropdown
  const suppliers = await prisma.supplier.findMany({
    where: { isActive: true },
    orderBy: { name: "asc" },
    select: { id: true, name: true }
  });

  // Get locations for dropdown
  const locations = await prisma.inventoryLocation.findMany({
    where: { isActive: true },
    orderBy: { name: "asc" },
    select: { id: true, name: true }
  });

  // Get all distinct categories for autocomplete
  const categories = await prisma.inventoryPart.groupBy({
    by: ["category"],
    where: {
      isActive: true,
      category: { not: null }
    }
  });

  // Get all distinct types for autocomplete
  const types = await prisma.inventoryPart.groupBy({
    by: ["type"],
    where: {
      isActive: true,
      type: { not: null }
    }
  });

  return json({ suppliers, locations, categories, types });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);

  const formData = await request.formData();
  const name = formData.get("name") as string;
  const partNumber = formData.get("partNumber") as string || null;
  const serialNumber = formData.get("serialNumber") as string || null;
  const description = formData.get("description") as string || null;
  const category = formData.get("category") as string || null;
  const type = formData.get("type") as string || null;
  const manufacturer = formData.get("manufacturer") as string || null;
  const model = formData.get("model") as string || null;
  const sku = formData.get("sku") as string || null;
  const barcode = formData.get("barcode") as string || null;
  const currentStock = parseInt(formData.get("currentStock") as string || "0", 10);
  const minimumStock = parseInt(formData.get("minimumStock") as string || "1", 10);
  const reorderPoint = parseInt(formData.get("reorderPoint") as string || "5", 10);
  const costPrice = formData.get("costPrice") ? parseFloat(formData.get("costPrice") as string) : null;
  const sellingPrice = formData.get("sellingPrice") ? parseFloat(formData.get("sellingPrice") as string) : null;
  const taxRate = parseFloat(formData.get("taxRate") as string || "0");
  const locationString = formData.get("location") as string || null;
  const unitOfMeasure = formData.get("unitOfMeasure") as string || "EA";
  const notes = formData.get("notes") as string || null;
  const supplierId = formData.get("supplierId") as string || null;
  const locationId = formData.get("locationId") as string || null;

  // Validate required fields
  if (!name) {
    return json({ error: "Part name is required" }, { status: 400 });
  }

  try {
    // Create part
    const part = await prisma.inventoryPart.create({
      data: {
        name,
        partNumber,
        serialNumber,
        description,
        category,
        type,
        manufacturer,
        model,
        sku,
        barcode,
        currentStock,
        minimumStock,
        reorderPoint,
        costPrice,
        sellingPrice,
        taxRate,
        location: locationString,
        unitOfMeasure,
        notes,
        supplierId,
      },
    });

    // If locationId is provided, create a part location record
    if (locationId) {
      await prisma.partLocation.create({
        data: {
          quantity: currentStock,
          partId: part.id,
          locationId,
        },
      });
    }

    // If there's initial stock, create an inventory transaction
    if (currentStock > 0) {
      await prisma.inventoryTransaction.create({
        data: {
          type: "INBOUND",
          quantity: currentStock,
          unitPrice: costPrice,
          totalPrice: costPrice ? costPrice * currentStock : null,
          referenceType: "INITIAL",
          notes: "Initial stock",
          partId: part.id,
          destinationLocationId: locationId,
        },
      });
    }

    return redirect(`/inventory/parts/${part.id}`);
  } catch (error) {
    console.error("Error creating part:", error);
    return json({ error: "An error occurred while creating the part" }, { status: 500 });
  }
}

export default function NewPart() {
  const { suppliers, locations, categories, types } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();

  const isSubmitting = navigation.state === "submitting";

  // State for form fields that need special handling
  const [unitOfMeasure, setUnitOfMeasure] = useState("EA");
  const [customUnitOfMeasure, setCustomUnitOfMeasure] = useState("");

  const commonUnitsOfMeasure = [
    { value: "EA", label: "Each (EA)" },
    { value: "PCS", label: "Pieces (PCS)" },
    { value: "KG", label: "Kilogram (KG)" },
    { value: "LTR", label: "Liter (LTR)" },
    { value: "M", label: "Meter (M)" },
    { value: "BOX", label: "Box (BOX)" },
    { value: "ROLL", label: "Roll (ROLL)" },
    { value: "SET", label: "Set (SET)" },
    { value: "CUSTOM", label: "Custom..." },
  ];

  return (
    <div>
      <div className="mb-6 flex items-center">
        <Link to="/inventory" className="mr-4 text-gray-500 hover:text-gray-700">
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>
        <h2 className="text-xl font-semibold">Add New Part</h2>
      </div>

      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Part Information</h3>
        </div>
        <div className="px-6 py-4">
          <Form method="post">
            {actionData?.error && (
              <div className="mb-4 rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{actionData.error}</h3>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {/* Basic Information */}
              <div className="space-y-4">
                <h4 className="text-md font-medium text-gray-800">Basic Information</h4>

                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Part Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="partNumber" className="block text-sm font-medium text-gray-700">
                    Part Number
                  </label>
                  <input
                    type="text"
                    id="partNumber"
                    name="partNumber"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="serialNumber" className="block text-sm font-medium text-gray-700">
                    Serial Number
                  </label>
                  <input
                    type="text"
                    id="serialNumber"
                    name="serialNumber"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                    Category
                  </label>
                  <input
                    type="text"
                    id="category"
                    name="category"
                    list="category-list"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                  <datalist id="category-list">
                    {categories.map((category) => (
                      <option key={category.category} value={category.category} />
                    ))}
                  </datalist>
                </div>

                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                    Type
                  </label>
                  <input
                    type="text"
                    id="type"
                    name="type"
                    list="type-list"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                  <datalist id="type-list">
                    {types.map((type) => (
                      <option key={type.type} value={type.type} />
                    ))}
                  </datalist>
                </div>

                <div>
                  <label htmlFor="manufacturer" className="block text-sm font-medium text-gray-700">
                    Manufacturer
                  </label>
                  <input
                    type="text"
                    id="manufacturer"
                    name="manufacturer"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="model" className="block text-sm font-medium text-gray-700">
                    Compatible Model
                  </label>
                  <input
                    type="text"
                    id="model"
                    name="model"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              {/* Inventory & Pricing Information */}
              <div className="space-y-4">
                <h4 className="text-md font-medium text-gray-800">Inventory & Pricing</h4>

                <div>
                  <label htmlFor="currentStock" className="block text-sm font-medium text-gray-700">
                    Current Stock
                  </label>
                  <input
                    type="number"
                    id="currentStock"
                    name="currentStock"
                    min="0"
                    defaultValue="0"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="minimumStock" className="block text-sm font-medium text-gray-700">
                    Minimum Stock
                  </label>
                  <input
                    type="number"
                    id="minimumStock"
                    name="minimumStock"
                    min="0"
                    defaultValue="1"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="reorderPoint" className="block text-sm font-medium text-gray-700">
                    Reorder Point
                  </label>
                  <input
                    type="number"
                    id="reorderPoint"
                    name="reorderPoint"
                    min="0"
                    defaultValue="5"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="costPrice" className="block text-sm font-medium text-gray-700">
                    Cost Price
                  </label>
                  <div className="relative mt-1 rounded-md shadow-sm">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">PLN</span>
                    </div>
                    <input
                      type="number"
                      id="costPrice"
                      name="costPrice"
                      min="0"
                      step="0.01"
                      className="block w-full rounded-md border border-gray-300 pl-12 pr-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="sellingPrice" className="block text-sm font-medium text-gray-700">
                    Selling Price
                  </label>
                  <div className="relative mt-1 rounded-md shadow-sm">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">PLN</span>
                    </div>
                    <input
                      type="number"
                      id="sellingPrice"
                      name="sellingPrice"
                      min="0"
                      step="0.01"
                      className="block w-full rounded-md border border-gray-300 pl-12 pr-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="taxRate" className="block text-sm font-medium text-gray-700">
                    Tax Rate (%)
                  </label>
                  <input
                    type="number"
                    id="taxRate"
                    name="taxRate"
                    min="0"
                    max="100"
                    defaultValue="23"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="unitOfMeasure" className="block text-sm font-medium text-gray-700">
                    Unit of Measure
                  </label>
                  <select
                    id="unitOfMeasure"
                    name="unitOfMeasure"
                    value={unitOfMeasure}
                    onChange={(e) => setUnitOfMeasure(e.target.value)}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  >
                    {commonUnitsOfMeasure.map((unit) => (
                      <option key={unit.value} value={unit.value}>
                        {unit.label}
                      </option>
                    ))}
                  </select>

                  {unitOfMeasure === "CUSTOM" && (
                    <div className="mt-2">
                      <label htmlFor="customUnitOfMeasure" className="block text-sm font-medium text-gray-700">
                        Custom Unit
                      </label>
                      <input
                        type="text"
                        id="customUnitOfMeasure"
                        value={customUnitOfMeasure}
                        onChange={(e) => setCustomUnitOfMeasure(e.target.value)}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                      />
                      <input
                        type="hidden"
                        name="unitOfMeasure"
                        value={customUnitOfMeasure || "CUSTOM"}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Location & Supplier Information */}
            <div className="mt-6 space-y-4">
              <h4 className="text-md font-medium text-gray-800">Location & Supplier</h4>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">
                    Warehouse Location
                  </label>
                  <select
                    id="locationId"
                    name="locationId"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="">Select a location</option>
                    {locations.map((location) => (
                      <option key={location.id} value={location.id}>
                        {location.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                    Storage Position
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    placeholder="e.g., Shelf A3, Bin 12, etc."
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="supplierId" className="block text-sm font-medium text-gray-700">
                  Supplier
                </label>
                <select
                  id="supplierId"
                  name="supplierId"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                >
                  <option value="">Select a supplier</option>
                  {suppliers.map((supplier) => (
                    <option key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                  Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  rows={3}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <Link
                to="/inventory"
                className="mr-4 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? "Saving..." : "Save Part"}
              </button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}