import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useNavigation } from "@remix-run/react";
import { requireUser } from "~/session.server";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { CustomFieldsSection } from "~/components/dynamic/CustomFieldsSection";
import { getCustomFields } from "~/models/metadata.server";
import { getEntityCustomFields, saveEntityCustomFields } from "~/models/custom-fields.server";
import { getCustomer } from "~/models/customer.server";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const user = await requireUser(request);
  const { customerId } = params;

  if (!customerId) {
    throw new Response("Customer ID is required", { status: 400 });
  }

  // Get customer
  const customer = await getCustomer(customerId);

  if (!customer) {
    throw new Response("Customer not found", { status: 404 });
  }

  // Get custom fields for customers
  const customFields = await getCustomFields('CUSTOMER', user.id);

  // Get custom field values for this customer
  const customFieldValues = await getEntityCustomFields(customerId, 'CUSTOMER');

  return json({
    customer,
    customFields,
    customFieldValues
  });
};

export const action = async ({ request, params }: ActionFunctionArgs) => {
  const user = await requireUser(request);
  const { customerId } = params;

  if (!customerId) {
    throw new Response("Customer ID is required", { status: 400 });
  }

  const formData = await request.formData();
  const customFieldsData = formData.get("customFieldsData");

  if (!customFieldsData) {
    return json({ error: "No custom field data provided" });
  }

  try {
    const parsedData = JSON.parse(customFieldsData as string);

    // Save custom field values
    const result = await saveEntityCustomFields(customerId, 'CUSTOMER', parsedData);

    if (!result.success) {
      return json({ error: result.error });
    }

    return json({ success: true });
  } catch (error) {
    console.error("Error saving custom fields:", error);
    return json({ error: "Failed to save custom fields" });
  }
};

export default function CustomerCustomFields() {
  const { customer, customFields, customFieldValues } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const handleSave = async (values: Record<string, any>) => {
    // This is handled by the form submission
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Custom Fields for {customer.name}</CardTitle>
        </CardHeader>
        <CardContent>
          {customFields.length === 0 ? (
            <p>No custom fields have been defined for customers yet. You can create custom fields in the Settings.</p>
          ) : (
            <CustomFieldsSection
              entityType="CUSTOMER"
              entityId={customer.id}
              fields={customFields}
              values={customFieldValues}
              onSave={handleSave}
            />
          )}

          {actionData?.error && (
            <div className="text-destructive mt-4">
              {actionData.error}
            </div>
          )}

          {actionData?.success && (
            <div className="text-green-600 mt-4">
              Custom fields saved successfully!
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}