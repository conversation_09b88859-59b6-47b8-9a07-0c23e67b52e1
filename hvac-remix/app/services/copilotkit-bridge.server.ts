/**
 * CopilotKit Bridge Service for HVAC-Remix
 * Bridges HVAC-Remix CRM with TruBackend CopilotKit Integration
 */

import type {
  EmailData,
  EmailAnalysis,
  MemoryResults,
  BielikResponse,
  CustomerContext,
  ServiceOrderData,
  DocumentAnalysisRequest,
  TruBackendHealthStatus
} from '~/types/copilotkit-bridge';

// TruBackend service configuration
const TRUBACKEND_CONFIG = {
  baseUrl: process.env.TRUBACKEND_BASE_URL || 'http://localhost:8000',
  emailIntelligence: process.env.TRUBACKEND_EMAIL_URL || 'http://localhost:8000',
  memoryBank: process.env.TRUBACKEND_MEMORY_URL || 'http://localhost:8004',
  bielikIntegration: process.env.TRUBACKEND_BIELIK_URL || 'http://localhost:8005',
  executiveAssistant: process.env.TRUBACKEND_EXECUTIVE_URL || 'http://localhost:8003',
  langchainAutomation: process.env.TRUBACKEND_LANGCHAIN_URL || 'http://localhost:8002',
  timeout: 30000,
  retries: 3,
};

/**
 * Main CopilotKit Bridge Service
 * Provides seamless integration between HVAC-Remix and TruBackend AI services
 */
export class CopilotKitBridge {
  private async request<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<{ success: boolean; data?: T; error?: string }> {
    const requestOptions: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      signal: AbortSignal.timeout(TRUBACKEND_CONFIG.timeout),
    };

    let lastError: Error;
    
    for (let attempt = 0; attempt <= TRUBACKEND_CONFIG.retries; attempt++) {
      try {
        const response = await fetch(url, requestOptions);
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          return {
            success: false,
            error: `HTTP ${response.status}: ${errorData.message || response.statusText}`,
          };
        }

        const data = await response.json();
        return { success: true, data };
      } catch (error) {
        lastError = error as Error;
        if (attempt < TRUBACKEND_CONFIG.retries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt)));
        }
      }
    }

    return {
      success: false,
      error: `Network Error: ${lastError.message}`,
    };
  }

  /**
   * Email Intelligence Integration
   */
  async analyzeEmail(email: EmailData): Promise<EmailAnalysis> {
    const result = await this.request<EmailAnalysis>(
      `${TRUBACKEND_CONFIG.emailIntelligence}/api/process-email`,
      {
        method: 'POST',
        body: JSON.stringify(email),
      }
    );

    if (!result.success) {
      throw new Error(`Email analysis failed: ${result.error}`);
    }

    return result.data!;
  }

  async analyzeCustomerIssue(
    customerMessage: string,
    customerId?: string,
    urgency: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<{
    analysis: any;
    recommendations: string[];
    similar_cases: any[];
    technical_insights: string;
    confidence: number;
  }> {
    // Analyze with Email Intelligence
    const emailAnalysis = await this.analyzeEmail({
      content: customerMessage,
      customer_id: customerId,
      urgency_level: urgency,
      from: customerId ? `customer-${customerId}@hvac.local` : '<EMAIL>',
      subject: 'HVAC Service Request',
      timestamp: new Date().toISOString(),
    });

    // Search for similar issues in Memory Bank
    const similarIssues = await this.searchMemoryBank(
      `HVAC issue: ${customerMessage}`,
      customerId
    );

    // Get Bielik V3 technical analysis
    const bielikAnalysis = await this.askBielik(
      `Analyze this HVAC issue and provide technical recommendations: ${customerMessage}`,
      {
        customer_id: customerId,
        similar_issues: similarIssues.results?.slice(0, 3),
        urgency_level: urgency,
      }
    );

    return {
      analysis: emailAnalysis.semantic_analysis,
      recommendations: emailAnalysis.response_suggestion?.priority_actions || [],
      similar_cases: similarIssues.results || [],
      technical_insights: bielikAnalysis.response,
      confidence: emailAnalysis.confidence_score,
    };
  }

  /**
   * Memory Bank Integration
   */
  async searchMemoryBank(
    query: string,
    customerId?: string,
    limit: number = 10
  ): Promise<MemoryResults> {
    const result = await this.request<MemoryResults>(
      `${TRUBACKEND_CONFIG.memoryBank}/api/search`,
      {
        method: 'POST',
        body: JSON.stringify({ query, customer_id: customerId, limit }),
      }
    );

    if (!result.success) {
      throw new Error(`Memory Bank search failed: ${result.error}`);
    }

    return result.data!;
  }

  async storeCustomerInteraction(
    customerId: string,
    interaction: {
      type: string;
      content: string;
      metadata?: any;
    }
  ): Promise<{ success: boolean; memory_id?: string }> {
    const result = await this.request(
      `${TRUBACKEND_CONFIG.memoryBank}/api/store`,
      {
        method: 'POST',
        body: JSON.stringify({
          customer_id: customerId,
          ...interaction,
          timestamp: new Date().toISOString(),
        }),
      }
    );

    return result;
  }

  /**
   * Bielik V3 Integration
   */
  async askBielik(
    prompt: string,
    context?: any,
    taskType: 'semantic_analysis' | 'intent_detection' | 'technical_classification' = 'semantic_analysis'
  ): Promise<BielikResponse> {
    const result = await this.request<BielikResponse>(
      `${TRUBACKEND_CONFIG.bielikIntegration}/api/analyze`,
      {
        method: 'POST',
        body: JSON.stringify({
          prompt,
          context,
          task_type: taskType,
        }),
      }
    );

    if (!result.success) {
      throw new Error(`Bielik analysis failed: ${result.error}`);
    }

    return result.data!;
  }

  /**
   * Executive Assistant Integration
   */
  async generateResponse(
    emailContext: any,
    tone: 'professional' | 'friendly' | 'urgent' | 'apologetic' = 'professional',
    includeScheduling: boolean = false
  ): Promise<{
    draft_response: string;
    suggested_actions: string[];
    scheduling_options: any[];
    confidence: number;
  }> {
    const result = await this.request(
      `${TRUBACKEND_CONFIG.executiveAssistant}/api/generate-response`,
      {
        method: 'POST',
        body: JSON.stringify({
          email_context: emailContext,
          tone,
          include_scheduling: includeScheduling,
        }),
      }
    );

    if (!result.success) {
      throw new Error(`Response generation failed: ${result.error}`);
    }

    return result.data!;
  }

  /**
   * Service Order Operations
   */
  async optimizeServiceRoute(orders: ServiceOrderData[]): Promise<{
    optimized_route: any[];
    estimated_time: number;
    cost_savings: number;
  }> {
    const result = await this.request(
      `${TRUBACKEND_CONFIG.langchainAutomation}/api/optimize-route`,
      {
        method: 'POST',
        body: JSON.stringify({ service_orders: orders }),
      }
    );

    if (!result.success) {
      throw new Error(`Route optimization failed: ${result.error}`);
    }

    return result.data!;
  }

  async predictMaintenanceNeeds(
    deviceId: string,
    deviceData: any
  ): Promise<{
    predictions: any[];
    recommended_actions: string[];
    timeline: string;
    confidence: number;
  }> {
    const result = await this.request(
      `${TRUBACKEND_CONFIG.langchainAutomation}/api/predict-maintenance`,
      {
        method: 'POST',
        body: JSON.stringify({
          device_id: deviceId,
          device_data: deviceData,
        }),
      }
    );

    if (!result.success) {
      throw new Error(`Maintenance prediction failed: ${result.error}`);
    }

    return result.data!;
  }

  /**
   * Document Analysis Operations
   */
  async analyzeDocument(
    documentPath: string,
    documentType: 'manual' | 'invoice' | 'photo' | 'report',
    metadata?: any
  ): Promise<{
    analysis: any;
    extracted_data: any;
    confidence: number;
    processing_time: number;
  }> {
    const result = await this.request(
      `${TRUBACKEND_CONFIG.langchainAutomation}/api/analyze-document`,
      {
        method: 'POST',
        body: JSON.stringify({
          document_path: documentPath,
          document_type: documentType,
          metadata,
        }),
      }
    );

    if (!result.success) {
      throw new Error(`Document analysis failed: ${result.error}`);
    }

    return result.data!;
  }

  /**
   * System Health and Monitoring
   */
  async getTruBackendStatus(): Promise<TruBackendHealthStatus> {
    const services = [
      { name: 'Email Intelligence', url: `${TRUBACKEND_CONFIG.emailIntelligence}/health` },
      { name: 'Memory Bank', url: `${TRUBACKEND_CONFIG.memoryBank}/health` },
      { name: 'Bielik Integration', url: `${TRUBACKEND_CONFIG.bielikIntegration}/health` },
      { name: 'Executive Assistant', url: `${TRUBACKEND_CONFIG.executiveAssistant}/health` },
      { name: 'Langchain Automation', url: `${TRUBACKEND_CONFIG.langchainAutomation}/health` },
    ];

    const statusChecks = await Promise.allSettled(
      services.map(async (service) => {
        try {
          const response = await fetch(service.url, {
            method: 'GET',
            signal: AbortSignal.timeout(5000),
          });
          return {
            name: service.name,
            status: response.ok ? 'healthy' : 'unhealthy',
            url: service.url,
            response_time: Date.now(),
          };
        } catch (error) {
          return {
            name: service.name,
            status: 'unreachable',
            url: service.url,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      })
    );

    const results = statusChecks.map((result, index) =>
      result.status === 'fulfilled' ? result.value : {
        name: services[index].name,
        status: 'error',
        url: services[index].url,
        error: 'Failed to check status',
      }
    );

    const healthyServices = results.filter(r => r.status === 'healthy').length;
    const totalServices = results.length;

    return {
      overall_status: healthyServices === totalServices ? 'all_healthy' : 'partial_issues',
      healthy_services: healthyServices,
      total_services: totalServices,
      services: results,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Customer Context Enrichment
   */
  async enrichCustomerContext(customerId: string): Promise<CustomerContext> {
    // Get customer data from Memory Bank
    const memoryResults = await this.searchMemoryBank(
      `customer profile interactions history`,
      customerId,
      20
    );

    // Get recent service history
    const serviceHistory = await this.searchMemoryBank(
      `service orders maintenance history`,
      customerId,
      10
    );

    // Analyze customer patterns with Bielik
    const patternAnalysis = await this.askBielik(
      `Analyze customer patterns and preferences based on interaction history`,
      {
        customer_id: customerId,
        interactions: memoryResults.results,
        service_history: serviceHistory.results,
      },
      'intent_detection'
    );

    return {
      customer_id: customerId,
      interaction_history: memoryResults.results || [],
      service_history: serviceHistory.results || [],
      patterns: patternAnalysis.response,
      risk_factors: [], // TODO: Implement risk analysis
      preferences: {}, // TODO: Extract from patterns
      last_updated: new Date().toISOString(),
    };
  }
}

// Singleton instance
let copilotKitBridge: CopilotKitBridge | null = null;

export function getCopilotKitBridge(): CopilotKitBridge {
  if (!copilotKitBridge) {
    copilotKitBridge = new CopilotKitBridge();
  }
  return copilotKitBridge;
}

// Export for use in CopilotKit actions
export const copilotKitBridge = getCopilotKitBridge();