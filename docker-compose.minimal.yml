version: '3.8'

# HVAC-Remix Minimal - Just the app with basic CopilotKit

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hvac-postgres-minimal
    environment:
      POSTGRES_DB: hvac_crm
      POSTGRES_USER: hvac_user
      POSTGRES_PASSWORD: hvac_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    networks:
      - hvac-minimal-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvac_user -d hvac_crm"]
      interval: 10s
      timeout: 5s
      retries: 5

  # HVAC-Remix with CopilotKit
  hvac-remix:
    build:
      context: ./hvac-remix
      dockerfile: Dockerfile.fixed
    container_name: hvac-remix-minimal
    environment:
      - DATABASE_URL=**************************************************/hvac_crm
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - NODE_ENV=development
      - PORT=3000
      - SESSION_SECRET=hvac-secret-key
    ports:
      - "3002:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - hvac-minimal-network

volumes:
  postgres_data:

networks:
  hvac-minimal-network:
    driver: bridge