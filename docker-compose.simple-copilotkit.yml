version: '3.8'

# HVAC-Remix + CopilotKit - Simple Working Version
# Uses existing TruBackend services

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hvac-postgres-simple
    environment:
      POSTGRES_DB: hvac_crm
      POSTGRES_USER: hvac_user
      POSTGRES_PASSWORD: hvac_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    networks:
      - hvac-simple-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvac_user -d hvac_crm"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: hvac-redis-simple
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_data:/data
    networks:
      - hvac-simple-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # HVAC-Remix with CopilotKit (Simple)
  hvac-remix:
    build:
      context: ./hvac-remix
      dockerfile: Dockerfile.simple
    container_name: hvac-remix-simple
    environment:
      # Database
      - DATABASE_URL=**************************************************/hvac_crm
      - REDIS_URL=redis://redis:6379
      
      # CopilotKit Configuration
      - COPILOTKIT_API_KEY=${COPILOTKIT_API_KEY:-}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      
      # TruBackend Integration (External services)
      - TRUBACKEND_BASE_URL=http://host.docker.internal:8000
      - TRUBACKEND_EMAIL_URL=http://host.docker.internal:8000
      - TRUBACKEND_MEMORY_URL=http://host.docker.internal:8000
      - TRUBACKEND_BIELIK_URL=http://host.docker.internal:8000
      - TRUBACKEND_EXECUTIVE_URL=http://host.docker.internal:8000
      - TRUBACKEND_LANGCHAIN_URL=http://host.docker.internal:8000
      
      # Application Settings
      - NODE_ENV=development
      - PORT=3000
      - SESSION_SECRET=${SESSION_SECRET:-hvac-secret-key}
      
    ports:
      - "3001:3000"  # Different port to avoid conflicts
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hvac-simple-network
    volumes:
      - ./hvac-remix/uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:

networks:
  hvac-simple-network:
    driver: bridge