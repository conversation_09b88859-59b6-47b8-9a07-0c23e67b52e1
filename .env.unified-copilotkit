# HVAC-Remix + TruBackend + CopilotKit - Unified Environment Configuration

# ===== DATABASE CONFIGURATION =====
DATABASE_URL=**************************************************/hvac_crm
REDIS_URL=redis://redis:6379

# ===== COPILOTKIT CONFIGURATION =====
COPILOTKIT_API_KEY=your_copilotkit_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
COPILOTKIT_RUNTIME_URL=/api/copilotkit
COPILOTKIT_TIMEOUT=30000
COPILOTKIT_RETRIES=3

# ===== TRUBACKEND INTEGRATION =====
TRUBACKEND_BASE_URL=http://trubackend-orchestrator:8000
TRUBACKEND_EMAIL_URL=http://trubackend-orchestrator:8000
TRUBACKEND_MEMORY_URL=http://trubackend-memory:8004
TRUBACKEND_BIELIK_URL=http://trubackend-bielik:8005
TRUBACKEND_EXECUTIVE_URL=http://trubackend-executive:8003
TRUBACKEND_LANGCHAIN_URL=http://trubackend-langchain:8002

# ===== AI MODEL CONFIGURATION =====
HUGGINGFACE_TOKEN=your_huggingface_token_here
BIELIK_MODEL_PATH=/models/bielik-v3
GEMMA_MODEL_PATH=/models/gemma-3-4b-it

# ===== APPLICATION SETTINGS =====
NODE_ENV=production
PORT=3000
SESSION_SECRET=your_super_secret_session_key_here
HOSTNAME=0.0.0.0

# ===== EXTERNAL API KEYS =====
AZURE_COMPUTER_VISION_KEY=your_azure_cv_key_here
AZURE_COMPUTER_VISION_ENDPOINT=your_azure_cv_endpoint_here
MICROSOFT_GRAPH_CLIENT_ID=your_graph_client_id_here
MICROSOFT_GRAPH_CLIENT_SECRET=your_graph_client_secret_here

# ===== LANGSMITH CONFIGURATION =====
LANGSMITH_API_KEY=your_langsmith_api_key_here
LANGSMITH_PROJECT=hvac-crm-copilotkit

# ===== MONITORING CONFIGURATION =====
PROMETHEUS_ENABLED=true
GRAFANA_ADMIN_PASSWORD=admin
GRAFANA_ALLOW_SIGN_UP=false

# ===== SECURITY SETTINGS =====
CORS_ORIGIN=http://localhost:3000,http://localhost:80
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# ===== DOCKER CONFIGURATION =====
COMPOSE_PROJECT_NAME=hvac-copilotkit
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1

# ===== GPU CONFIGURATION =====
CUDA_VISIBLE_DEVICES=0
NVIDIA_VISIBLE_DEVICES=all
NVIDIA_DRIVER_CAPABILITIES=compute,utility