version: '3.8'

# HVAC-Remix + TruBackend + CopilotKit - Working Version
# Uproszczona wersja z d<PERSON>łającymi komponentami

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hvac-postgres
    environment:
      POSTGRES_DB: hvac_crm
      POSTGRES_USER: hvac_user
      POSTGRES_PASSWORD: hvac_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvac_user -d hvac_crm"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: hvac-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # TruBackend Simplified (All AI services in one)
  trubackend-simplified:
    build:
      context: ./TruBackend
      dockerfile: Dockerfile.simplified
    container_name: trubackend-simplified
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - DATABASE_URL=**************************************************/hvac_crm
      - REDIS_URL=redis://redis:6379
    ports:
      - "8000:8000"  # Main API
      - "8501:8501"  # Gradio Interface
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hvac-network
    volumes:
      - ./TruBackend/logs:/app/logs
      - ./TruBackend/data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # HVAC-Remix with CopilotKit (Simplified)
  hvac-remix:
    build:
      context: ./hvac-remix
      dockerfile: Dockerfile.simple
    container_name: hvac-remix-copilotkit
    environment:
      # Database
      - DATABASE_URL=**************************************************/hvac_crm
      - REDIS_URL=redis://redis:6379
      
      # CopilotKit Configuration
      - COPILOTKIT_API_KEY=${COPILOTKIT_API_KEY:-}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      
      # TruBackend Integration (Simplified)
      - TRUBACKEND_BASE_URL=http://trubackend-simplified:8000
      - TRUBACKEND_EMAIL_URL=http://trubackend-simplified:8000
      - TRUBACKEND_MEMORY_URL=http://trubackend-simplified:8000
      - TRUBACKEND_BIELIK_URL=http://trubackend-simplified:8000
      - TRUBACKEND_EXECUTIVE_URL=http://trubackend-simplified:8000
      - TRUBACKEND_LANGCHAIN_URL=http://trubackend-simplified:8000
      
      # Application Settings
      - NODE_ENV=development
      - PORT=3000
      - SESSION_SECRET=${SESSION_SECRET:-hvac-secret-key}
      
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      trubackend-simplified:
        condition: service_healthy
    networks:
      - hvac-network
    volumes:
      - ./hvac-remix/uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Simplified)
  nginx:
    image: nginx:alpine
    container_name: hvac-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/simple.conf:/etc/nginx/nginx.conf
    depends_on:
      - hvac-remix
      - trubackend-simplified
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:

networks:
  hvac-network:
    driver: bridge