#!/bin/bash

# HVAC-Remix + TruBackend + CopilotKit - Unified Docker Startup Script

echo "🚀 Starting HVAC-Remix with CopilotKit and TruBackend Integration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env.unified-copilotkit" ]; then
    print_warning ".env.unified-copilotkit not found. Creating from template..."
    cp .env.unified-copilotkit.template .env.unified-copilotkit 2>/dev/null || {
        print_error "Template file not found. Please create .env.unified-copilotkit manually."
        exit 1
    }
fi

# Load environment variables
set -a
source .env.unified-copilotkit
set +a

print_status "Environment loaded from .env.unified-copilotkit"

# Check for required API keys
missing_keys=()

if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ]; then
    missing_keys+=("OPENAI_API_KEY")
fi

if [ -z "$HUGGINGFACE_TOKEN" ] || [ "$HUGGINGFACE_TOKEN" = "your_huggingface_token_here" ]; then
    missing_keys+=("HUGGINGFACE_TOKEN")
fi

if [ ${#missing_keys[@]} -gt 0 ]; then
    print_warning "Missing required API keys: ${missing_keys[*]}"
    print_warning "Please update .env.unified-copilotkit with your actual API keys"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p nginx/ssl
mkdir -p monitoring/prometheus
mkdir -p monitoring/grafana/dashboards
mkdir -p monitoring/grafana/datasources
mkdir -p models
mkdir -p hvac-remix/uploads
mkdir -p hvac-remix/public/uploads

# Check if CopilotKit is installed in hvac-remix
if [ ! -f "hvac-remix/package.json" ]; then
    print_error "hvac-remix/package.json not found. Please ensure HVAC-Remix is properly set up."
    exit 1
fi

# Install CopilotKit if not already installed
print_status "Checking CopilotKit installation..."
cd hvac-remix
if ! npm list @copilotkit/react-core > /dev/null 2>&1; then
    print_status "Installing CopilotKit dependencies..."
    npm install @copilotkit/react-core @copilotkit/react-ui @copilotkit/runtime
    print_success "CopilotKit dependencies installed"
else
    print_success "CopilotKit already installed"
fi
cd ..

# Build and start services
print_status "Building and starting all services..."

# Stop any existing containers
docker-compose -f docker-compose.unified-copilotkit.yml down

# Build images
print_status "Building Docker images..."
docker-compose -f docker-compose.unified-copilotkit.yml build --parallel

# Start infrastructure services first
print_status "Starting infrastructure services..."
docker-compose -f docker-compose.unified-copilotkit.yml up -d postgres redis qdrant

# Wait for infrastructure to be ready
print_status "Waiting for infrastructure services to be ready..."
sleep 10

# Check PostgreSQL health
print_status "Checking PostgreSQL connection..."
for i in {1..30}; do
    if docker-compose -f docker-compose.unified-copilotkit.yml exec -T postgres pg_isready -U hvac_user -d hvac_crm > /dev/null 2>&1; then
        print_success "PostgreSQL is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "PostgreSQL failed to start within 30 attempts"
        exit 1
    fi
    sleep 2
done

# Start TruBackend services
print_status "Starting TruBackend AI services..."
docker-compose -f docker-compose.unified-copilotkit.yml up -d \
    trubackend-memory \
    trubackend-executive \
    trubackend-langchain

# Wait for TruBackend services
print_status "Waiting for TruBackend services..."
sleep 15

# Start Bielik (GPU-intensive service)
print_status "Starting Bielik AI service (this may take a while)..."
docker-compose -f docker-compose.unified-copilotkit.yml up -d trubackend-bielik

# Start orchestrator
print_status "Starting TruBackend orchestrator..."
docker-compose -f docker-compose.unified-copilotkit.yml up -d trubackend-orchestrator

# Wait for TruBackend to be ready
print_status "Waiting for TruBackend orchestrator..."
sleep 10

# Start HVAC-Remix with CopilotKit
print_status "Starting HVAC-Remix with CopilotKit integration..."
docker-compose -f docker-compose.unified-copilotkit.yml up -d hvac-remix

# Start monitoring and proxy
print_status "Starting monitoring and proxy services..."
docker-compose -f docker-compose.unified-copilotkit.yml up -d prometheus grafana nginx

# Final health check
print_status "Performing final health checks..."
sleep 15

# Check service health
services=("postgres" "redis" "qdrant" "trubackend-orchestrator" "trubackend-memory" "hvac-remix" "nginx")
failed_services=()

for service in "${services[@]}"; do
    if docker-compose -f docker-compose.unified-copilotkit.yml ps | grep -q "$service.*Up"; then
        print_success "$service is running"
    else
        print_error "$service failed to start"
        failed_services+=("$service")
    fi
done

if [ ${#failed_services[@]} -gt 0 ]; then
    print_error "Some services failed to start: ${failed_services[*]}"
    print_status "Check logs with: docker-compose -f docker-compose.unified-copilotkit.yml logs [service_name]"
    exit 1
fi

# Display access information
echo ""
print_success "🎉 HVAC-Remix with CopilotKit is now running!"
echo ""
echo "📋 Access Information:"
echo "  🏠 Main Application:     http://localhost"
echo "  🤖 CopilotKit API:       http://localhost/api/copilotkit"
echo "  🔧 TruBackend API:       http://localhost/trubackend/"
echo "  📊 Grafana Dashboard:    http://localhost:3001 (admin/admin)"
echo "  📈 Prometheus Metrics:   http://localhost:9090"
echo ""
echo "🔍 Service Status:"
docker-compose -f docker-compose.unified-copilotkit.yml ps
echo ""
echo "📚 Quick Commands:"
echo "  View logs:     docker-compose -f docker-compose.unified-copilotkit.yml logs -f [service]"
echo "  Stop all:      docker-compose -f docker-compose.unified-copilotkit.yml down"
echo "  Restart:       ./start-unified-copilotkit.sh"
echo ""
print_success "Ready to experience the power of AI-enhanced HVAC CRM! 🚀"