# 🐳 HVAC-Remix + TruBackend + CopilotKit - Unified Docker Guide

## 🎯 **Przegląd Systemu**

Kompletny system HVAC CRM z integracją AI w jednym, zunifikowanym środowisku Docker, zawierający:

### **🏗️ Architektura Kontenerów:**
- **hvac-remix-copilotkit** - Główna aplikacja CRM z CopilotKit
- **trubackend-orchestrator** - Orkiestrator AI (port 8000)
- **trubackend-memory** - Bank pamięci (port 8004)
- **trubackend-bielik** - Integracja Bielik V3 (port 8005)
- **trubackend-executive** - Asystent wykonawczy (port 8003)
- **trubackend-langchain** - Automatyzacja Langchain (port 8002)
- **postgres** - Baza danych PostgreSQL
- **redis** - Cache <PERSON>is
- **qdrant** - <PERSON>za wektorowa
- **nginx** - Reverse proxy
- **prometheus** - Monitoring
- **grafana** - Dashboard

---

## 🚀 **Szybki Start**

### **1. Przygotowanie Środowiska**
```bash
cd /home/<USER>/HVAC

# Skopiuj i skonfiguruj zmienne środowiskowe
cp .env.unified-copilotkit .env.local

# Edytuj plik .env.local i dodaj swoje klucze API:
# - OPENAI_API_KEY
# - HUGGINGFACE_TOKEN
# - COPILOTKIT_API_KEY (opcjonalnie)
```

### **2. Uruchomienie Systemu**
```bash
# Uruchom cały system jedną komendą
./start-unified-copilotkit.sh
```

### **3. Dostęp do Aplikacji**
- **🏠 Główna aplikacja**: http://localhost
- **🤖 CopilotKit API**: http://localhost/api/copilotkit
- **🔧 TruBackend API**: http://localhost/trubackend/
- **📊 Grafana**: http://localhost:3001 (admin/admin)
- **📈 Prometheus**: http://localhost:9090

---

## 📋 **Szczegółowa Konfiguracja**

### **Struktura Plików:**
```
/home/<USER>/HVAC/
├── docker-compose.unified-copilotkit.yml  # Główny plik Docker Compose
├── .env.unified-copilotkit                # Zmienne środowiskowe
├── start-unified-copilotkit.sh           # Skrypt startowy
├── hvac-remix/
│   ├── Dockerfile.copilotkit             # Dockerfile dla HVAC-Remix
│   ├── app/services/copilotkit-bridge.server.ts
│   ├── app/components/HVACCopilotActions.tsx
│   └── app/components/EnhancedHVACDashboard.tsx
├── nginx/
│   └── unified-copilotkit.conf           # Konfiguracja Nginx
├── scripts/
│   └── init-multiple-databases.sh       # Inicjalizacja baz danych
└── monitoring/
    └── prometheus/
        └── prometheus-copilotkit.yml     # Konfiguracja Prometheus
```

### **Kluczowe Zmienne Środowiskowe:**
```bash
# API Keys (WYMAGANE)
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_TOKEN=your_huggingface_token_here

# CopilotKit (OPCJONALNE)
COPILOTKIT_API_KEY=your_copilotkit_api_key_here

# TruBackend URLs (AUTOMATYCZNE)
TRUBACKEND_BASE_URL=http://trubackend-orchestrator:8000
TRUBACKEND_MEMORY_URL=http://trubackend-memory:8004
TRUBACKEND_BIELIK_URL=http://trubackend-bielik:8005
```

---

## 🔧 **Zarządzanie Kontenerami**

### **Podstawowe Komendy:**
```bash
# Uruchomienie wszystkich usług
docker-compose -f docker-compose.unified-copilotkit.yml up -d

# Zatrzymanie wszystkich usług
docker-compose -f docker-compose.unified-copilotkit.yml down

# Przebudowanie obrazów
docker-compose -f docker-compose.unified-copilotkit.yml build

# Sprawdzenie statusu
docker-compose -f docker-compose.unified-copilotkit.yml ps

# Logi konkretnej usługi
docker-compose -f docker-compose.unified-copilotkit.yml logs -f hvac-remix

# Restart konkretnej usługi
docker-compose -f docker-compose.unified-copilotkit.yml restart hvac-remix
```

### **Monitoring i Debugowanie:**
```bash
# Sprawdzenie zdrowia wszystkich usług
docker-compose -f docker-compose.unified-copilotkit.yml ps

# Logi w czasie rzeczywistym
docker-compose -f docker-compose.unified-copilotkit.yml logs -f

# Wejście do kontenera
docker-compose -f docker-compose.unified-copilotkit.yml exec hvac-remix bash

# Sprawdzenie zasobów
docker stats

# Sprawdzenie sieci
docker network ls
docker network inspect hvac_hvac-network
```

---

## 🎯 **Funkcjonalności CopilotKit**

### **Dostępne Akcje AI:**
1. **analyzeCustomerIssue** - Analiza problemów HVAC z Bielik V3
2. **searchCustomerHistory** - Przeszukiwanie historii klienta
3. **askBielikExpert** - Konsultacja z ekspertem Bielik V3
4. **generateProfessionalResponse** - Generowanie odpowiedzi
5. **optimizeServiceRoute** - Optymalizacja tras serwisowych
6. **predictMaintenanceNeeds** - Predykcja konserwacji
7. **analyzeDocument** - Analiza dokumentów
8. **checkSystemStatus** - Sprawdzanie statusu systemu

### **Przykład Użycia:**
```typescript
// W komponencie React
import { EnhancedHVACDashboard } from '~/components/EnhancedHVACDashboard';

<EnhancedHVACDashboard 
  customerContext={customerData}
  currentPage="dashboard"
  userRole="technician"
>
  {/* Twoja zawartość dashboard */}
</EnhancedHVACDashboard>
```

---

## 🔍 **Rozwiązywanie Problemów**

### **Typowe Problemy:**

#### **1. Usługi nie startują**
```bash
# Sprawdź logi
docker-compose -f docker-compose.unified-copilotkit.yml logs

# Sprawdź zasoby systemowe
docker system df
docker system prune  # Oczyść nieużywane zasoby
```

#### **2. Brak połączenia z TruBackend**
```bash
# Sprawdź status TruBackend
curl http://localhost/trubackend/health

# Sprawdź logi orchestratora
docker-compose -f docker-compose.unified-copilotkit.yml logs trubackend-orchestrator
```

#### **3. Problemy z GPU (Bielik)**
```bash
# Sprawdź dostępność GPU
nvidia-smi

# Sprawdź logi Bielik
docker-compose -f docker-compose.unified-copilotkit.yml logs trubackend-bielik

# Restart usługi Bielik
docker-compose -f docker-compose.unified-copilotkit.yml restart trubackend-bielik
```

#### **4. Problemy z bazą danych**
```bash
# Sprawdź połączenie z PostgreSQL
docker-compose -f docker-compose.unified-copilotkit.yml exec postgres psql -U hvac_user -d hvac_crm -c "SELECT 1;"

# Sprawdź logi PostgreSQL
docker-compose -f docker-compose.unified-copilotkit.yml logs postgres
```

### **Health Checks:**
```bash
# Sprawdź wszystkie endpointy zdrowia
curl http://localhost/health
curl http://localhost/trubackend/health
curl http://localhost:8004/health  # Memory Bank
curl http://localhost:8005/health  # Bielik
curl http://localhost:8003/health  # Executive Assistant
```

---

## 📊 **Monitoring i Metryki**

### **Grafana Dashboards:**
- **URL**: http://localhost:3001
- **Login**: admin / admin
- **Dashboards**: Automatycznie skonfigurowane dla wszystkich usług

### **Prometheus Metrics:**
- **URL**: http://localhost:9090
- **Targets**: Wszystkie usługi HVAC i TruBackend
- **Alerts**: Skonfigurowane dla krytycznych metryk

### **Kluczowe Metryki:**
- Response time aplikacji
- Wykorzystanie GPU (Bielik)
- Wykorzystanie pamięci (Memory Bank)
- Liczba aktywnych połączeń
- Błędy API i timeouty

---

## 🔒 **Bezpieczeństwo**

### **Konfiguracja Nginx:**
- Rate limiting dla API
- CORS headers dla CopilotKit
- Security headers (XSS, CSRF protection)
- Proxy timeouts dla długich operacji AI

### **Zmienne Środowiskowe:**
- Wszystkie klucze API w .env
- Szyfrowanie komunikacji między usługami
- Izolacja sieciowa kontenerów

---

## 🚀 **Deployment Production**

### **Przygotowanie do Produkcji:**
```bash
# 1. Skonfiguruj SSL w Nginx
# 2. Ustaw production environment variables
# 3. Skonfiguruj backup bazy danych
# 4. Ustaw monitoring i alerty
# 5. Skonfiguruj load balancing (jeśli potrzebne)
```

### **Backup i Recovery:**
```bash
# Backup bazy danych
docker-compose -f docker-compose.unified-copilotkit.yml exec postgres pg_dump -U hvac_user hvac_crm > backup.sql

# Restore bazy danych
docker-compose -f docker-compose.unified-copilotkit.yml exec -T postgres psql -U hvac_user hvac_crm < backup.sql
```

---

## 🎉 **Podsumowanie**

Ten zunifikowany system Docker zapewnia:

✅ **Kompletną integrację** HVAC-Remix + TruBackend + CopilotKit
✅ **Jednokomendowe uruchomienie** całego systemu
✅ **Automatyczne health checks** i monitoring
✅ **Skalowalną architekturę** z load balancing
✅ **Production-ready** konfigurację z bezpieczeństwem
✅ **Pełne wsparcie GPU** dla modeli AI
✅ **Real-time monitoring** z Grafana i Prometheus

**System jest gotowy do użycia w środowisku produkcyjnym!** 🚀

---

*Przewodnik Docker stworzony dla HVAC-Remix CopilotKit Integration*  
*Data: Styczeń 2025*  
*Status: Gotowy do wdrożenia* ✅