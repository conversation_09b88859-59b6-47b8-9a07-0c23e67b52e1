version: '3.8'

# HVAC-Remix + TruBackend + CopilotKit - Unified Docker Compose
# Kompletny system z integracją AI w jednym kontenerze

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hvac-postgres
    environment:
      POSTGRES_DB: hvac_crm
      POSTGRES_USER: hvac_user
      POSTGRES_PASSWORD: hvac_password
      POSTGRES_MULTIPLE_DATABASES: hvac_crm,trubackend_memory,supabase_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh
    ports:
      - "5432:5432"
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvac_user -d hvac_crm"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: hvac-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # TruBackend Email Intelligence Orchestrator
  trubackend-orchestrator:
    build:
      context: ./TruBackend
      dockerfile: Dockerfile.orchestrator
    container_name: trubackend-orchestrator
    environment:
      - DATABASE_URL=**************************************************/trubackend_memory
      - REDIS_URL=redis://redis:6379
      - BIELIK_API_URL=http://trubackend-bielik:8005
      - MEMORY_BANK_URL=http://trubackend-memory:8004
      - EXECUTIVE_ASSISTANT_URL=http://trubackend-executive:8003
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TruBackend Memory Bank Service
  trubackend-memory:
    build:
      context: ./TruBackend/memory-bank
      dockerfile: Dockerfile
    container_name: trubackend-memory
    environment:
      - DATABASE_URL=**************************************************/trubackend_memory
      - REDIS_URL=redis://redis:6379
      - QDRANT_URL=http://qdrant:6333
    ports:
      - "8004:8004"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TruBackend Bielik Integration
  trubackend-bielik:
    build:
      context: ./TruBackend/bielik-integration
      dockerfile: Dockerfile
    container_name: trubackend-bielik
    environment:
      - BIELIK_MODEL_PATH=/models/bielik-v3
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
      - CUDA_VISIBLE_DEVICES=0
    ports:
      - "8005:8005"
    volumes:
      - ./models:/models
    networks:
      - hvac-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 60s
      timeout: 30s
      retries: 3

  # TruBackend Executive Assistant
  trubackend-executive:
    build:
      context: ./TruBackend/executive-ai-assistant
      dockerfile: Dockerfile
    container_name: trubackend-executive
    environment:
      - DATABASE_URL=**************************************************/trubackend_memory
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY}
    ports:
      - "8003:8003"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TruBackend Langchain Automation
  trubackend-langchain:
    build:
      context: ./TruBackend/langchain-automation
      dockerfile: Dockerfile
    container_name: trubackend-langchain
    environment:
      - DATABASE_URL=**************************************************/trubackend_memory
      - REDIS_URL=redis://redis:6379
      - BIELIK_API_URL=http://trubackend-bielik:8005
    ports:
      - "8002:8002"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: hvac-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # HVAC-Remix with CopilotKit Integration
  hvac-remix:
    build:
      context: ./hvac-remix
      dockerfile: Dockerfile.copilotkit
    container_name: hvac-remix-copilotkit
    environment:
      # Database
      - DATABASE_URL=**************************************************/hvac_crm
      - REDIS_URL=redis://redis:6379
      
      # CopilotKit Configuration
      - COPILOTKIT_API_KEY=${COPILOTKIT_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # TruBackend Integration
      - TRUBACKEND_BASE_URL=http://trubackend-orchestrator:8000
      - TRUBACKEND_EMAIL_URL=http://trubackend-orchestrator:8000
      - TRUBACKEND_MEMORY_URL=http://trubackend-memory:8004
      - TRUBACKEND_BIELIK_URL=http://trubackend-bielik:8005
      - TRUBACKEND_EXECUTIVE_URL=http://trubackend-executive:8003
      - TRUBACKEND_LANGCHAIN_URL=http://trubackend-langchain:8002
      
      # Application Settings
      - NODE_ENV=production
      - PORT=3000
      - SESSION_SECRET=${SESSION_SECRET}
      
      # External APIs
      - AZURE_COMPUTER_VISION_KEY=${AZURE_COMPUTER_VISION_KEY}
      - AZURE_COMPUTER_VISION_ENDPOINT=${AZURE_COMPUTER_VISION_ENDPOINT}
      - MICROSOFT_GRAPH_CLIENT_ID=${MICROSOFT_GRAPH_CLIENT_ID}
      - MICROSOFT_GRAPH_CLIENT_SECRET=${MICROSOFT_GRAPH_CLIENT_SECRET}
      
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      trubackend-orchestrator:
        condition: service_healthy
      trubackend-memory:
        condition: service_healthy
      trubackend-bielik:
        condition: service_healthy
      trubackend-executive:
        condition: service_healthy
    networks:
      - hvac-network
    volumes:
      - ./hvac-remix/uploads:/app/uploads
      - ./hvac-remix/public/uploads:/app/public/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: hvac-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/unified-copilotkit.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - hvac-remix
      - trubackend-orchestrator
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: hvac-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus-copilotkit.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - hvac-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: hvac-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - hvac-network

volumes:
  postgres_data:
  redis_data:
  qdrant_data:
  prometheus_data:
  grafana_data:

networks:
  hvac-network:
    driver: bridge