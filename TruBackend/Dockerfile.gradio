# 🚀 TruBackend Gradio Interface Dockerfile

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements-gradio.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-gradio.txt

# Copy application files
COPY gradio_interface.py .

# Create non-root user
RUN useradd -m -u 1000 gradio && chown -R gradio:gradio /app
USER gradio

# Expose port
EXPOSE 7860

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:7860 || exit 1

# Start command
CMD ["python", "gradio_interface.py"]