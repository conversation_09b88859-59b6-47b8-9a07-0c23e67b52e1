#!/bin/bash

# 🚀 Quick Launch Script for TruBackend Gradio Interface

echo "🚀 TruBackend Gradio Interface - Quick Launch"
echo "============================================="

cd /home/<USER>/HVAC/TruBackend

# Check if virtual environment exists
if [ ! -d "gradio-env" ]; then
    echo "❌ Virtual environment not found. Please run setup first:"
    echo "   python3 -m venv gradio-env"
    echo "   ./gradio-env/bin/pip install -r requirements-gradio.txt"
    exit 1
fi

echo "🎯 Choose interface to launch:"
echo "1) Test Interface (minimal, currently working)"
echo "2) Simple Interface (full features)"
echo "3) Check if already running"

read -p "Enter choice (1-3): " choice

case $choice in
    1)
        echo "🧪 Launching Test Interface..."
        ./gradio-env/bin/python gradio_test.py
        ;;
    2)
        echo "🎨 Launching Simple Interface..."
        ./gradio-env/bin/python gradio_simple.py
        ;;
    3)
        echo "🔍 Checking running processes..."
        if pgrep -f "gradio" > /dev/null; then
            echo "✅ Gradio is running!"
            echo "📊 Access at: http://localhost:7860"
            ps aux | grep gradio | grep -v grep
        else
            echo "❌ No Gradio processes found"
        fi
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac