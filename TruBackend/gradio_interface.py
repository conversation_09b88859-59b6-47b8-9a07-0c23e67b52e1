"""
🚀 TruBackend Comprehensive Gradio Interface

Advanced web interface for TruBackend management with:
- Real-time monitoring dashboard
- Email analysis interface
- System management controls
- Performance analytics
- AI model interaction
- Configuration management
"""

import asyncio
import logging
import json
import time
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import os
import requests
import threading

# Gradio imports
import gradio as gr

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
TRUBACKEND_API_URL = "http://localhost:8000"
GRADIO_PORT = 7860
UPDATE_INTERVAL = 5  # seconds

class TruBackendGradioInterface:
    """Comprehensive Gradio interface for TruBackend management"""
    
    def __init__(self):
        self.stats_history = []
        self.email_history = []
        self.system_logs = []
        self.is_monitoring = False
        self.monitoring_thread = None        
        # Initialize data storage
        self.performance_data = {
            'timestamps': [],
            'requests_processed': [],
            'avg_processing_time': [],
            'total_tokens': [],
            'memory_usage': [],
            'cpu_usage': []
        }
        
    def start_monitoring(self):
        """Start background monitoring thread"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            logger.info("Monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1)
        logger.info("Monitoring stopped")
    
    def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_monitoring:
            try:
                self._collect_stats()
                time.sleep(UPDATE_INTERVAL)
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(UPDATE_INTERVAL)
    
    def _collect_stats(self):
        """Collect statistics from TruBackend API"""
        try:
            response = requests.get(f"{TRUBACKEND_API_URL}/stats", timeout=5)
            if response.status_code == 200:
                stats = response.json()
                timestamp = datetime.now()
                
                # Store in history
                self.stats_history.append({
                    'timestamp': timestamp,
                    'stats': stats
                })
                
                # Update performance data
                self.performance_data['timestamps'].append(timestamp)
                self.performance_data['requests_processed'].append(
                    stats.get('processor_stats', {}).get('requests_processed', 0)
                )
                self.performance_data['avg_processing_time'].append(
                    stats.get('processor_stats', {}).get('avg_processing_time', 0)
                )
                self.performance_data['total_tokens'].append(
                    stats.get('processor_stats', {}).get('total_tokens', 0)
                )                
                # Simulate system metrics (in production, get from actual system)
                try:
                    import psutil
                    self.performance_data['memory_usage'].append(psutil.virtual_memory().percent)
                    self.performance_data['cpu_usage'].append(psutil.cpu_percent())
                except ImportError:
                    self.performance_data['memory_usage'].append(50.0)  # Mock data
                    self.performance_data['cpu_usage'].append(25.0)     # Mock data
                
                # Keep only last 100 data points
                for key in self.performance_data:
                    if len(self.performance_data[key]) > 100:
                        self.performance_data[key] = self.performance_data[key][-100:]
                
        except Exception as e:
            logger.error(f"Failed to collect stats: {e}")
    
    def get_system_status(self) -> Tuple[str, str, str]:
        """Get current system status"""
        try:
            response = requests.get(f"{TRUBACKEND_API_URL}/health", timeout=5)
            if response.status_code == 200:
                status = "🟢 Online"
                health_data = response.json()
                uptime = health_data.get('uptime', 'Unknown')
                last_check = datetime.now().strftime("%H:%M:%S")
            else:
                status = "🟡 Degraded"
                uptime = "Unknown"
                last_check = datetime.now().strftime("%H:%M:%S")
        except:
            status = "🔴 Offline"
            uptime = "Unknown"
            last_check = datetime.now().strftime("%H:%M:%S")
        
        return status, uptime, last_check
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current statistics"""
        try:
            response = requests.get(f"{TRUBACKEND_API_URL}/stats", timeout=5)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        
        return {
            'processor_stats': {
                'requests_processed': 0,
                'total_tokens': 0,
                'avg_processing_time': 0.0,
                'last_request': None
            },
            'system_info': {
                'model': 'gemma-3-4B-it-qat-GGUF',
                'max_context': 128000,
                'device': 'auto'
            }
        }    
    def analyze_email_api(self, subject: str, sender: str, recipient: str, content: str) -> Dict[str, Any]:
        """Analyze email using TruBackend API"""
        try:
            email_data = {
                'subject': subject,
                'sender': sender,
                'recipient': recipient,
                'content': content
            }
            
            response = requests.post(
                f"{TRUBACKEND_API_URL}/analyze-email",
                json=email_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # Store in history
                self.email_history.append({
                    'timestamp': datetime.now(),
                    'request': email_data,
                    'response': result
                })
                
                return result
            else:
                return {'error': f'API Error: {response.status_code}'}
                
        except Exception as e:
            return {'error': f'Request failed: {str(e)}'}
    
    def create_performance_chart(self) -> go.Figure:
        """Create performance monitoring chart"""
        if not self.performance_data['timestamps']:
            # Create empty chart
            fig = go.Figure()
            fig.add_annotation(
                text="No data available - Start monitoring to see performance metrics",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16)
            )
            fig.update_layout(
                title="Performance Metrics",
                xaxis_title="Time",
                yaxis_title="Value"
            )
            return fig
        
        fig = go.Figure()
        
        # Add traces for different metrics
        fig.add_trace(go.Scatter(
            x=self.performance_data['timestamps'],
            y=self.performance_data['requests_processed'],
            mode='lines+markers',
            name='Requests Processed',
            line=dict(color='#3498db')
        ))