version: '3.8'

services:
  trubackend-simplified:
    build:
      context: .
      dockerfile: Dockerfile.simplified
    container_name: trubackend-simplified
    ports:
      - "8000:8000"  # Main API and Dashboard
      - "8501:8501"  # Streamlit (if needed)
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  redis:
    image: redis:7-alpine
    container_name: trubackend-redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  redis_data:

networks:
  default:
    name: trubackend-network