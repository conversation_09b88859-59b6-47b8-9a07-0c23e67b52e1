#!/bin/bash

# 🚀 TruBackend Gradio Interface Startup Script (Fixed)

echo "🚀 Starting TruBackend Gradio Interface..."
echo "=================================================="

# Check if TruBackend is running
echo "🔍 Checking TruBackend status..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ TruBackend is running"
else
    echo "⚠️  TruBackend is not running - starting it first..."
    ./start-simplified.sh &
    sleep 10
fi

# Check if virtual environment exists
if [ ! -d "gradio-env" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv gradio-env
fi

# Install Gradio requirements if needed
echo "📦 Installing Gradio requirements..."
./gradio-env/bin/pip install -r requirements-gradio.txt

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export LOG_LEVEL="INFO"

# Start Gradio interface
echo "🎨 Starting Gradio Interface..."
echo "📊 Interface will be available at: http://localhost:7860"
echo "🔗 TruBackend API: http://localhost:8000"
echo ""
echo "Press Ctrl+C to stop the interface"
echo ""

./gradio-env/bin/python gradio_simple.py