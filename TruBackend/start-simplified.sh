#!/bin/bash

# TruBackend Simplified Startup Script

echo "🚀 Starting TruBackend Simplified..."
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Create necessary directories
mkdir -p logs data

# Build and start the services
echo "🔨 Building TruBackend Simplified..."
docker-compose -f docker-compose.simplified.yml build

echo "🚀 Starting services..."
docker-compose -f docker-compose.simplified.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check health
echo "🔍 Checking service health..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ TruBackend Simplified is running!"
    echo ""
    echo "📊 Dashboard: http://localhost:8000/dashboard"
    echo "🔗 API Docs: http://localhost:8000/docs"
    echo "❤️  Health Check: http://localhost:8000/health"
    echo "📈 Stats: http://localhost:8000/stats"
    echo ""
    echo "🐳 Docker Status:"
    docker-compose -f docker-compose.simplified.yml ps
else
    echo "❌ Service health check failed. Checking logs..."
    docker-compose -f docker-compose.simplified.yml logs trubackend-simplified
fi

echo "=================================================="
echo "🎯 TruBackend Simplified is ready for testing!"