# 🚀 TruBackend Comprehensive Gradio Interface

**Advanced Web Interface for TruBackend Management**

## 🎯 Overview

The TruBackend Gradio Interface provides a sophisticated, user-friendly web interface for managing and monitoring the TruBackend email intelligence system. Built with Gradio, it offers real-time monitoring, interactive email analysis, system management, and comprehensive analytics.

## ✨ Key Features

### 📊 Real-time Dashboard
- **Live System Monitoring**: Real-time status, uptime, and health checks
- **Performance Metrics**: Interactive charts for requests, processing time, and resource usage
- **Token Usage Analytics**: Track AI model token consumption over time
- **System Information**: Detailed model and configuration display

### 📧 Interactive Email Analysis
- **Email Testing Interface**: User-friendly forms for email input and analysis
- **Real-time Results**: Instant sentiment, priority, and entity analysis
- **Analysis History**: Track and review previous email analyses
- **Example Templates**: Pre-loaded HVAC industry email examples

### 🤖 AI Model Management
- **Model Information**: Display current model configuration and capabilities
- **Direct Model Interaction**: Test AI model responses directly
- **Performance Tuning**: Adjust temperature and token limits
- **Processing Statistics**: Monitor model performance metrics

### ⚙️ System Management
- **Service Controls**: Start, stop, and restart TruBackend services
- **Resource Monitoring**: CPU, memory, and disk usage tracking
- **System Logs**: Real-time log viewing and filtering
- **Health Diagnostics**: Comprehensive system health checks

### 📈 Advanced Analytics
- **Historical Data**: Analyze trends over custom date ranges
- **Sentiment Distribution**: Visual breakdown of email sentiment patterns
- **Priority Analysis**: Track email priority classifications
- **Export Capabilities**: Generate and download analytics reports

## 🚀 Quick Start

### Method 1: Standalone Launch
```bash
# Install requirements
pip install -r requirements-gradio.txt

# Start the interface
./start-gradio.sh
```

### Method 2: Docker Compose
```bash
# Start TruBackend + Gradio Interface
docker-compose -f docker-compose.gradio.yml up -d

# View logs
docker-compose -f docker-compose.gradio.yml logs -f gradio-interface
```

### Method 3: Manual Python Launch
```bash
# Set environment variables
export TRUBACKEND_API_URL="http://localhost:8000"
export LOG_LEVEL="INFO"

# Launch interface
python gradio_interface.py
```

## 🌐 Access Points

- **Gradio Interface**: http://localhost:7860
- **TruBackend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 📋 Interface Tabs

### 📊 Dashboard Tab
- System status indicators
- Real-time performance charts
- Monitoring controls (Start/Stop/Refresh)
- Key metrics display
- Token usage visualization

### 📧 Email Analysis Tab
- Email input form (Subject, Sender, Recipient, Content)
- Analysis results display
- Processing statistics
- Analysis history table
- Example email templates

### 🤖 AI Model Tab
- Model configuration display
- Direct text processing interface
- Parameter adjustment controls
- Model performance metrics

### ⚙️ System Management Tab
- Service control buttons
- Resource usage monitoring
- System log viewer
- Health check utilities

### 📈 Analytics Tab
- Historical data visualization
- Custom date range selection
- Sentiment and priority distributions
- Export functionality

## 🔧 Configuration

### Environment Variables
```bash
TRUBACKEND_API_URL=http://localhost:8000  # TruBackend API endpoint
GRADIO_PORT=7860                          # Gradio interface port
LOG_LEVEL=INFO                            # Logging level
UPDATE_INTERVAL=5                         # Monitoring update interval (seconds)
```

### Interface Customization
The interface supports custom CSS styling and theme configuration:
- Soft theme with professional color scheme
- Responsive design for various screen sizes
- Custom status indicators and metric cards
- Interactive charts with zoom and pan capabilities

## 📊 Monitoring Features

### Real-time Metrics
- **Requests Processed**: Total email analyses completed
- **Token Usage**: AI model token consumption tracking
- **Processing Time**: Average response time monitoring
- **System Resources**: CPU, memory, and disk usage

### Performance Charts
- Interactive Plotly visualizations
- Multi-axis charts for different metric types
- Historical trend analysis
- Automatic data point management (last 100 points)

### Alert System
- Visual status indicators (🟢 Online, 🟡 Degraded, 🔴 Offline)
- Real-time health monitoring
- Automatic refresh capabilities
- Error handling and display

## 🧪 Testing Features

### Email Analysis Testing
- Pre-loaded HVAC industry examples:
  - Customer complaint scenarios
  - Meeting request templates
  - Sales inquiry examples
- Real-time analysis results
- Processing time measurement
- Confidence scoring

### System Testing
- Health check endpoints
- API connectivity testing
- Performance benchmarking
- Load testing capabilities

## 🔒 Security Features

### Access Control
- Non-root container execution
- Secure API communication
- Input validation and sanitization
- Error handling without sensitive data exposure

### Data Protection
- No persistent storage of sensitive email content
- Secure API token handling
- HTTPS support ready
- CORS configuration

## 🐳 Docker Configuration

### Container Architecture
- **Base Image**: Python 3.11-slim
- **User**: Non-root execution (gradio:1000)
- **Health Checks**: Automatic service monitoring
- **Networking**: Isolated container network

### Volume Mounts
- Log directory for persistent logging
- Configuration files for customization
- Data directory for analytics export

## 📈 Performance Optimization

### Frontend Optimization
- Efficient data loading and caching
- Optimized chart rendering
- Lazy loading for large datasets
- Responsive design principles

### Backend Integration
- Asynchronous API calls
- Connection pooling
- Request timeout handling
- Error recovery mechanisms

## 🔍 Troubleshooting

### Common Issues

#### Interface Not Loading
```bash
# Check if Gradio is running
curl http://localhost:7860

# Check container status
docker-compose -f docker-compose.gradio.yml ps

# View logs
docker-compose -f docker-compose.gradio.yml logs gradio-interface
```

#### API Connection Issues
```bash
# Verify TruBackend is running
curl http://localhost:8000/health

# Check network connectivity
docker network ls
docker network inspect trubackend-network
```

#### Performance Issues
```bash
# Monitor resource usage
docker stats

# Check system resources
htop
df -h
```

### Log Analysis
```bash
# View Gradio logs
tail -f logs/gradio-interface.log

# View TruBackend logs
tail -f logs/trubackend.log

# Filter error logs
grep ERROR logs/*.log
```

## 🚀 Advanced Usage

### Custom Themes
```python
# Create custom theme
custom_theme = gr.themes.Soft().set(
    body_background_fill="*neutral_50",
    button_primary_background_fill="*primary_500"
)

# Apply to interface
demo = gr.Blocks(theme=custom_theme)
```

### API Integration
```python
# Custom API endpoints
@app.get("/custom-metrics")
async def get_custom_metrics():
    return {"custom_data": "value"}

# Integration with Gradio
def fetch_custom_data():
    response = requests.get("http://localhost:8000/custom-metrics")
    return response.json()
```

### Analytics Export
```python
# Export analytics data
def export_analytics(date_range, metrics):
    data = collect_analytics_data(date_range, metrics)
    return generate_report(data)
```

## 📚 Dependencies

### Core Requirements
- **gradio>=5.10.0**: Main interface framework
- **pandas>=2.2.3**: Data manipulation
- **plotly>=5.24.0**: Interactive visualizations
- **requests>=2.32.0**: HTTP client
- **psutil>=6.1.0**: System monitoring

### Optional Enhancements
- **streamlit**: Alternative interface option
- **dash**: Advanced dashboard capabilities
- **bokeh**: Additional visualization options

## 🎉 Success Metrics

✅ **Comprehensive Interface**: Complete management dashboard  
✅ **Real-time Monitoring**: Live performance tracking  
✅ **Interactive Analysis**: User-friendly email testing  
✅ **System Management**: Full service control  
✅ **Advanced Analytics**: Historical data analysis  
✅ **Docker Integration**: Containerized deployment  
✅ **Professional UI**: Modern, responsive design  

**The TruBackend Gradio Interface is ready for production use! 🚀**