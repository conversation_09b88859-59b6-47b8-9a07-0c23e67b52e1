#!/usr/bin/env python3
"""
🚀 TruBackend Gradio Test Interface

A minimal test interface to verify Gradio is working.
"""

import gradio as gr

def greet(name):
    return f"Hello {name}! TruBackend Gradio is working! 🚀"

def test_api():
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            return "🟢 TruBackend API is online!"
        else:
            return f"🟡 TruBackend API responded with status {response.status_code}"
    except Exception as e:
        return f"🔴 TruBackend API is offline: {str(e)}"

# Create simple interface
with gr.Blocks(title="🚀 TruBackend Test") as demo:
    gr.HTML("<h1>🚀 TruBackend Gradio Test Interface</h1>")
    
    with gr.Row():
        name_input = gr.Textbox(label="Your Name", placeholder="Enter your name...")
        greet_btn = gr.But<PERSON>("👋 Greet", variant="primary")
        greeting_output = gr.Textbox(label="Greeting", interactive=False)
    
    with gr.Row():
        api_test_btn = gr.<PERSON><PERSON>("🔍 Test TruBackend API", variant="secondary")
        api_status = gr.Textbox(label="API Status", interactive=False)
    
    greet_btn.click(greet, inputs=name_input, outputs=greeting_output)
    api_test_btn.click(test_api, outputs=api_status)

if __name__ == "__main__":
    print("🚀 Starting TruBackend Gradio Test Interface...")
    print("📊 Interface URL: http://localhost:7860")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        quiet=False
    )