# 🚀 TruBackend Simplified

**Email Intelligence with Gemma-3-4B Integration & Web Monitoring**

## 🎯 Overview

TruBackend Simplified is a streamlined version of the full TruBackend system, focusing on core email intelligence capabilities with the Gemma-3-4B-it-qat-GGUF model and a comprehensive web monitoring interface.

## ✨ Key Features

### 🤖 AI/ML Core
- **Gemma-3-4B Integration**: Latest quantized model from HuggingFace
- **128k Context Length**: Support for long email threads
- **Multimodal Support**: Text and image processing capabilities
- **Real-time Analysis**: Fast email sentiment, priority, and entity extraction

### 📊 Web Monitoring Dashboard
- **Real-time Metrics**: Live performance monitoring
- **Interactive Testing**: Built-in email analysis testing interface
- **Visual Analytics**: Plotly-powered charts and graphs
- **Health Monitoring**: System status and component health checks

### 🔧 Simplified Architecture
- **FastAPI Backend**: Modern async API framework
- **Single Container**: Simplified deployment model
- **Redis Integration**: Fast caching and session management
- **Docker Compose**: Easy multi-service orchestration

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- 8GB+ RAM (for Gemma-3-4B model)
- CUDA-compatible GPU (optional, for acceleration)

### 1. Start TruBackend Simplified
```bash
./start-simplified.sh
```

### 2. Access the Dashboard
- **Web Dashboard**: http://localhost:8000/dashboard
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 📋 API Endpoints

### Core Endpoints
- `GET /` - System information
- `GET /health` - Health check
- `GET /stats` - Processing statistics
- `POST /analyze-email` - Email analysis
- `GET /dashboard` - Web monitoring interface

### Email Analysis Request
```json
{
  "subject": "Important Meeting Tomorrow",
  "content": "Hi team, we have an urgent meeting tomorrow at 9 AM...",
  "sender": "<EMAIL>",
  "recipient": "<EMAIL>"
}
```

### Analysis Response
```json
{
  "email_id": "email_123",
  "sentiment": "neutral",
  "priority": "high",
  "summary": "Meeting scheduled for tomorrow at 9 AM",
  "entities": ["email:<EMAIL>"],
  "confidence": 0.85,
  "processing_time": 0.234
}
```

## 🔧 Configuration

### Environment Variables
- `LOG_LEVEL`: Logging level (INFO, DEBUG, WARNING)
- `PYTHONPATH`: Python module path
- `MODEL_CACHE_DIR`: Model cache directory

### Model Configuration
The system uses the latest Gemma-3-4B-it-qat-GGUF model:
- **Repository**: lmstudio-community/gemma-3-4B-it-qat-GGUF
- **Context Length**: 128k tokens
- **Max Output**: 8192 tokens
- **Quantization**: 4-bit QAT optimized

## 📊 Monitoring Features

### Real-time Metrics
- **Requests Processed**: Total email analyses
- **Token Usage**: Total tokens processed
- **Processing Time**: Average response time
- **Last Request**: Timestamp of latest analysis

### Performance Charts
- Interactive Plotly visualizations
- Real-time data updates every 5 seconds
- Historical performance tracking

### Testing Interface
- Built-in email analysis testing
- Form-based input for quick testing
- Real-time result display
- Error handling and validation

## 🐳 Docker Services

### TruBackend Simplified
- **Port**: 8000 (API + Dashboard)
- **Health Check**: Automatic monitoring
- **Restart Policy**: Unless stopped

### Redis Cache
- **Port**: 6379
- **Persistence**: Enabled
- **Volume**: Persistent data storage

## 🔍 Troubleshooting

### Check Service Status
```bash
docker-compose -f docker-compose.simplified.yml ps
```

### View Logs
```bash
docker-compose -f docker-compose.simplified.yml logs trubackend-simplified
```

### Restart Services
```bash
docker-compose -f docker-compose.simplified.yml restart
```

### Stop Services
```bash
docker-compose -f docker-compose.simplified.yml down
```

## 🎯 Next Steps

### Immediate Testing
1. Open the dashboard at http://localhost:8000/dashboard
2. Use the email analysis testing form
3. Monitor real-time performance metrics
4. Check API documentation at /docs

### Production Deployment
1. Configure environment variables
2. Set up SSL/TLS certificates
3. Configure reverse proxy (nginx)
4. Set up monitoring and alerting
5. Scale with Docker Swarm or Kubernetes

## 📈 Performance Optimization

### Model Optimization
- Use GPU acceleration when available
- Implement model quantization
- Cache frequent analyses
- Batch processing for multiple emails

### System Optimization
- Redis caching for repeated queries
- Async processing for concurrent requests
- Connection pooling for database access
- Load balancing for high traffic

## 🔒 Security Considerations

### API Security
- Input validation and sanitization
- Rate limiting for API endpoints
- CORS configuration
- Authentication middleware (future)

### Container Security
- Non-root user execution
- Minimal base image
- Security scanning
- Regular updates

## 📚 Dependencies

### Core Dependencies (Latest Versions)
- **FastAPI**: 0.115.0+ (Web framework)
- **Transformers**: 4.52.0+ (AI models)
- **PyTorch**: 2.7.0+ (ML backend)
- **Streamlit**: 1.40.0+ (Web interface)
- **Plotly**: 5.24.0+ (Visualizations)

### Full dependency list in `requirements-simplified.txt`

---

## 🎉 Success Metrics

✅ **Simplified Architecture**: Reduced from 15+ components to 5 core modules  
✅ **Latest Dependencies**: All packages updated to latest stable versions  
✅ **Gemma-3-4B Integration**: Latest quantized model with 128k context  
✅ **Web Monitoring**: Complete dashboard with real-time metrics  
✅ **Docker Optimization**: Single container with health checks  
✅ **Testing Interface**: Built-in email analysis testing  
✅ **Performance Monitoring**: Real-time charts and statistics  

**TruBackend Simplified is ready for production testing! 🚀**