# TruBackend Simplified - Gemma-3-4B with Web Monitoring
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements-simplified.txt .
RUN pip install --no-cache-dir -r requirements-simplified.txt

# Copy application files
COPY trubackend_simplified.py .
COPY trubackend-config.json .

# Create logs directory
RUN mkdir -p logs

# Create non-root user
RUN useradd -m trubackend && chown -R trubackend:trubackend /app

# Switch to non-root user
USER trubackend

# Expose ports
EXPOSE 8000 8501

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start command
CMD ["python", "trubackend_simplified.py"]