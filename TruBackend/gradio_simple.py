"""
🚀 TruBackend Simple Gradio Interface

A simplified but functional Gradio interface for TruBackend management.
"""

import gradio as gr
import requests
import json
from datetime import datetime
import pandas as pd

# Configuration
TRUBACKEND_API_URL = "http://localhost:8000"

def get_system_status():
    """Get current system status"""
    try:
        response = requests.get(f"{TRUBACKEND_API_URL}/health", timeout=5)
        if response.status_code == 200:
            return "🟢 Online", "System is running"
        else:
            return "🟡 Degraded", "System responding with errors"
    except:
        return "🔴 Offline", "Cannot connect to TruBackend"

def analyze_email(subject, sender, recipient, content):
    """Analyze email using TruBackend API"""
    if not subject or not content:
        return "❌ Error: Subject and content are required", "", "", 0, 0, ""
    
    try:
        email_data = {
            'subject': subject,
            'sender': sender,
            'recipient': recipient,
            'content': content
        }
        
        response = requests.post(
            f"{TRUBACKEND_API_URL}/analyze-email",
            json=email_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return (
                "✅ Analysis completed",
                result.get('sentiment', ''),
                result.get('priority', ''),
                result.get('confidence', 0),
                result.get('processing_time', 0),
                result.get('summary', '')
            )
        else:
            return f"❌ API Error: {response.status_code}", "", "", 0, 0, ""
            
    except Exception as e:
        return f"❌ Request failed: {str(e)}", "", "", 0, 0, ""

def load_example_email(example_type):
    """Load example email"""
    examples = {
        "complaint": {
            "subject": "Urgent: HVAC System Failure - Immediate Assistance Required",
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "content": "I am extremely frustrated with your service! Our HVAC system has been down for 3 days and no one has responded to our calls. This is unacceptable and we demand immediate action."
        },
        "meeting": {
            "subject": "Weekly HVAC Maintenance Review Meeting",
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "content": "Hi team, let's schedule our weekly maintenance review meeting for Thursday at 2 PM. We'll discuss recent service calls and performance metrics."
        }
    }
    
    example = examples.get(example_type, examples["complaint"])
    return example["subject"], example["sender"], example["recipient"], example["content"]

# Create Gradio interface
with gr.Blocks(title="🚀 TruBackend Interface", theme=gr.themes.Soft()) as demo:
    
    # Header
    gr.HTML("""
    <div style="text-align: center; padding: 20px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 20px;">
        <h1 style="margin: 0; font-size: 2.5em;">🚀 TruBackend Interface</h1>
        <p style="margin: 10px 0 0 0; font-size: 1.2em;">Email Intelligence & AI Model Management</p>
    </div>
    """)
    
    with gr.Tabs():
        # Dashboard Tab
        with gr.Tab("📊 Dashboard"):
            with gr.Row():
                status_text = gr.Textbox(label="System Status", interactive=False)
                status_detail = gr.Textbox(label="Details", interactive=False)
                refresh_btn = gr.Button("🔄 Refresh Status", variant="primary")
            
            refresh_btn.click(get_system_status, outputs=[status_text, status_detail])
        
        # Email Analysis Tab
        with gr.Tab("📧 Email Analysis"):
            with gr.Row():
                with gr.Column():
                    gr.Markdown("### ✉️ Email Input")
                    email_subject = gr.Textbox(label="Subject", placeholder="Enter email subject...")
                    email_sender = gr.Textbox(label="Sender", placeholder="<EMAIL>")
                    email_recipient = gr.Textbox(label="Recipient", placeholder="<EMAIL>")
                    email_content = gr.Textbox(label="Content", lines=6, placeholder="Enter email content...")
                    
                    with gr.Row():
                        analyze_btn = gr.Button("🔍 Analyze Email", variant="primary")
                        clear_btn = gr.Button("🗑️ Clear", variant="secondary")
                    
                    with gr.Row():
                        example1_btn = gr.Button("📧 Customer Complaint")
                        example2_btn = gr.Button("📧 Meeting Request")
                
                with gr.Column():
                    gr.Markdown("### 📋 Analysis Results")
                    analysis_status = gr.Textbox(label="Status", interactive=False)
                    sentiment_result = gr.Textbox(label="😊 Sentiment", interactive=False)
                    priority_result = gr.Textbox(label="⚡ Priority", interactive=False)
                    confidence_result = gr.Number(label="🎯 Confidence", interactive=False, precision=2)
                    processing_time_result = gr.Number(label="⏱️ Processing Time (s)", interactive=False, precision=3)
                    summary_result = gr.Textbox(label="📝 Summary", lines=3, interactive=False)
    
    # Event handlers
    analyze_btn.click(
        analyze_email,
        inputs=[email_subject, email_sender, email_recipient, email_content],
        outputs=[analysis_status, sentiment_result, priority_result, confidence_result, processing_time_result, summary_result]
    )
    
    clear_btn.click(
        lambda: ("", "", "", ""),
        outputs=[email_subject, email_sender, email_recipient, email_content]
    )
    
    example1_btn.click(
        lambda: load_example_email("complaint"),
        outputs=[email_subject, email_sender, email_recipient, email_content]
    )
    
    example2_btn.click(
        lambda: load_example_email("meeting"),
        outputs=[email_subject, email_sender, email_recipient, email_content]
    )
    
    # Load initial status
    demo.load(get_system_status, outputs=[status_text, status_detail])

if __name__ == "__main__":
    print("🚀 Starting TruBackend Simple Gradio Interface...")
    print(f"📊 Interface URL: http://localhost:7860")
    print(f"🔗 TruBackend API: {TRUBACKEND_API_URL}")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )