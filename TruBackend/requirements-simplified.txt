# TruBackend Simplified Requirements - Latest Stable Versions

# Core Framework
fastapi>=0.115.0
uvicorn[standard]>=0.34.0
pydantic>=2.11.0
pydantic-settings>=2.9.0

# Web Interface & Monitoring
streamlit>=1.40.0
plotly>=5.24.0
dash>=2.18.0

# AI/ML Core - Gemma-3-4B Focus
transformers>=4.52.0
torch>=2.7.0
sentence-transformers>=4.1.0
huggingface-hub>=0.32.0

# Database & Storage
sqlalchemy>=2.0.41
alembic>=1.16.1
redis>=6.1.0

# Email Processing
email-validator>=2.2.0
mail-parser>=4.1.2
imapclient>=3.0.1

# Data Processing
pandas>=2.2.3
numpy>=2.2.6
python-dateutil>=2.9.0

# Configuration & Environment
python-dotenv>=1.1.0
pyyaml>=6.0.2
toml>=0.10.2

# Logging & Monitoring
structlog>=25.3.0
prometheus-client>=0.22.0
sentry-sdk[fastapi]>=2.29.0

# Security
cryptography>=45.0.0
python-jose[cryptography]>=3.4.0
passlib[bcrypt]>=1.7.4

# HTTP & Async
httpx>=0.28.0
aiohttp>=3.12.0
aiofiles>=23.2.0,<24.0
websockets>=11.0,<15.0

# Utilities
click>=8.2.0
rich>=14.0.0
typer>=0.15.0
jinja2>=3.1.6

# Testing
pytest>=8.3.5
pytest-asyncio>=0.26.0