#!/usr/bin/env python3
"""
TruBackend Email Intelligence Orchestrator

Główny orchestrator dla analizy danych mailowych z pełną integracją AI/ML.
Koordynuje wszystkie komponenty: email-integration, langchain-automation,
executive-ai-assistant, memory-bank, i bielik-integration.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path

# Import komponentów TruBackend
import sys
sys.path.append(str(Path(__file__).parent))

sys.path.append(str(Path(__file__).parent / "email-integration"))
sys.path.append(str(Path(__file__).parent / "langchain-automation"))
sys.path.append(str(Path(__file__).parent / "executive-ai-assistant"))
sys.path.append(str(Path(__file__).parent / "memory-bank"))
sys.path.append(str(Path(__file__).parent / "bielik-integration"))

from email_processor import EmailProcessor
from core.llm.router import <PERSON><PERSON>outer
from eaia.main.graph import create_graph
from services.memory_service import MemoryBankService
from bielik_connector import BielikConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class EmailAnalysisResult:
    """Wynik analizy emaila przez TruBackend."""
    email_id: str
    analysis_timestamp: datetime
    semantic_analysis: Dict[str, Any]
    intent_classification: Dict[str, Any]
    context_enrichment: Dict[str, Any]
    response_suggestion: Dict[str, Any]
    memory_bank_updates: List[Dict[str, Any]]
    confidence_score: float
    processing_time_ms: int

class EmailIntelligenceOrchestrator:
    """
    Główny orchestrator dla Email Intelligence w TruBackend.

    Koordynuje pełny pipeline analizy emaili:
    1. Email Ingestion (email-integration)
    2. Semantic Analysis (bielik-integration)
    3. Intent Classification (langchain-automation)
    4. Context Enrichment (memory-bank)
    5. Response Generation (executive-ai-assistant)
    6. Memory Bank Update (memory-bank)
    """

    def __init__(self, config_path: str = "trubackend-config.json"):
        """Initialize orchestrator z konfiguracją TruBackend."""
        self.config = self._load_config(config_path)
        self.email_processor = None
        self.llm_router = None
        self.executive_assistant = None
        self.memory_bank = None
        self.bielik_connector = None

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Załaduj konfigurację TruBackend."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return {}

    async def initialize(self):
        """Inicjalizuj wszystkie komponenty TruBackend."""
        logger.info("🚀 Initializing TruBackend Email Intelligence Orchestrator...")

        try:
            # Initialize Email Processor
            self.email_processor = EmailProcessor(
                config=self.config.get('email_integration', {})
            )
            await self.email_processor.initialize()
            logger.info("✅ Email Processor initialized")

            # Initialize LLM Router
            self.llm_router = LLMRouter(
                config=self.config.get('ai_models', {})
            )
            await self.llm_router.initialize()
            logger.info("✅ LLM Router initialized")

            # Initialize Bielik Connector
            self.bielik_connector = BielikConnector(
                config=self.config.get('bielik_integration', {})
            )
            await self.bielik_connector.initialize()
            logger.info("✅ Bielik Connector initialized")

            # Initialize Memory Bank
            self.memory_bank = MemoryBankService(
                config=self.config.get('memory_bank', {})
            )
            await self.memory_bank.initialize()
            logger.info("✅ Memory Bank initialized")

            # Initialize Executive Assistant
            self.executive_assistant = create_graph(
                config=self.config.get('executive_ai_assistant', {})
            )
            logger.info("✅ Executive AI Assistant initialized")

            logger.info("🎯 TruBackend Email Intelligence Orchestrator ready!")

        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            raise

    async def process_email(self, email_data: Dict[str, Any]) -> EmailAnalysisResult:
        """
        Przetwórz email przez pełny pipeline Email Intelligence.

        Args:
            email_data: Raw email data

        Returns:
            EmailAnalysisResult: Kompletny wynik analizy
        """
        start_time = datetime.now()
        email_id = email_data.get('id', f"email_{int(start_time.timestamp())}")

        logger.info(f"📧 Processing email {email_id} through TruBackend pipeline...")

        try:
            # Stage 1: Email Ingestion & Preprocessing
            processed_email = await self.email_processor.process_raw_email(email_data)
            logger.info(f"✅ Stage 1: Email ingestion completed for {email_id}")

            # Stage 2: Semantic Analysis with Bielik V3
            semantic_analysis = await self.bielik_connector.analyze_email_semantics(
                processed_email['content']
            )
            logger.info(f"✅ Stage 2: Semantic analysis completed for {email_id}")

            # Stage 3: Intent Classification with LLM Router
            intent_classification = await self.llm_router.classify_intent(
                processed_email['content'],
                context=semantic_analysis
            )
            logger.info(f"✅ Stage 3: Intent classification completed for {email_id}")

            # Stage 4: Context Enrichment from Memory Bank
            context_enrichment = await self.memory_bank.enrich_email_context(
                email_data=processed_email,
                semantic_analysis=semantic_analysis,
                intent_classification=intent_classification
            )
            logger.info(f"✅ Stage 4: Context enrichment completed for {email_id}")

            # Stage 5: Response Generation with Executive Assistant
            response_suggestion = await self.executive_assistant.generate_response(
                email_data=processed_email,
                analysis_context={
                    'semantic': semantic_analysis,
                    'intent': intent_classification,
                    'context': context_enrichment
                }
            )
            logger.info(f"✅ Stage 5: Response generation completed for {email_id}")

            # Stage 6: Memory Bank Update
            memory_updates = await self.memory_bank.update_from_email_analysis(
                email_id=email_id,
                analysis_results={
                    'semantic': semantic_analysis,
                    'intent': intent_classification,
                    'context': context_enrichment,
                    'response': response_suggestion
                }
            )
            logger.info(f"✅ Stage 6: Memory bank update completed for {email_id}")

            # Calculate processing time and confidence
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            confidence_score = self._calculate_confidence_score(
                semantic_analysis, intent_classification, context_enrichment
            )

            # Create result
            result = EmailAnalysisResult(
                email_id=email_id,
                analysis_timestamp=start_time,
                semantic_analysis=semantic_analysis,
                intent_classification=intent_classification,
                context_enrichment=context_enrichment,
                response_suggestion=response_suggestion,
                memory_bank_updates=memory_updates,
                confidence_score=confidence_score,
                processing_time_ms=int(processing_time)
            )

            logger.info(f"🎯 Email {email_id} processed successfully in {processing_time:.2f}ms")
            logger.info(f"📊 Confidence score: {confidence_score:.2f}")

            return result

        except Exception as e:
            logger.error(f"❌ Error processing email {email_id}: {e}")
            raise

    def _calculate_confidence_score(
        self,
        semantic: Dict[str, Any],
        intent: Dict[str, Any],
        context: Dict[str, Any]
    ) -> float:
        """Oblicz confidence score na podstawie wyników analizy."""
        try:
            semantic_confidence = semantic.get('confidence', 0.0)
            intent_confidence = intent.get('confidence', 0.0)
            context_confidence = context.get('confidence', 0.0)

            # Weighted average
            total_confidence = (
                semantic_confidence * 0.4 +
                intent_confidence * 0.4 +
                context_confidence * 0.2
            )

            return min(max(total_confidence, 0.0), 1.0)

        except Exception as e:
            logger.warning(f"Error calculating confidence score: {e}")
            return 0.5

    async def batch_process_emails(self, email_batch: List[Dict[str, Any]]) -> List[EmailAnalysisResult]:
        """Przetwórz batch emaili równolegle."""
        logger.info(f"📦 Processing batch of {len(email_batch)} emails...")

        tasks = [self.process_email(email) for email in email_batch]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Error processing email {i}: {result}")
            else:
                successful_results.append(result)

        logger.info(f"✅ Batch processing completed: {len(successful_results)}/{len(email_batch)} successful")
        return successful_results

    async def get_processing_stats(self) -> Dict[str, Any]:
        """Pobierz statystyki przetwarzania."""
        return {
            'email_processor': await self.email_processor.get_stats() if self.email_processor else {},
            'llm_router': await self.llm_router.get_stats() if self.llm_router else {},
            'memory_bank': await self.memory_bank.get_stats() if self.memory_bank else {},
            'bielik_connector': await self.bielik_connector.get_stats() if self.bielik_connector else {}
        }

async def main():
    """Main function for testing TruBackend Email Intelligence."""
    orchestrator = EmailIntelligenceOrchestrator()
    await orchestrator.initialize()

    # Test email data
    test_email = {
        'id': 'test_001',
        'from': '<EMAIL>',
        'to': '<EMAIL>',
        'subject': 'Urgent: AC unit not working',
        'content': 'Hello, our air conditioning unit stopped working this morning. It\'s very hot and we need immediate assistance. The unit is making strange noises.',
        'timestamp': datetime.now().isoformat()
    }

    # Process test email
    result = await orchestrator.process_email(test_email)

    print(f"🎯 Email Analysis Result:")
    print(f"Email ID: {result.email_id}")
    print(f"Processing Time: {result.processing_time_ms}ms")
    print(f"Confidence Score: {result.confidence_score:.2f}")
    print(f"Intent: {result.intent_classification.get('primary_intent', 'unknown')}")
    print(f"Urgency: {result.semantic_analysis.get('urgency_level', 'unknown')}")

# FastAPI app for HTTP endpoints
from fastapi import FastAPI
import time

app = FastAPI(title="TruBackend Email Intelligence Orchestrator", version="1.0.0")
start_time = time.time()

# Global orchestrator instance
orchestrator_instance = None

@app.on_event("startup")
async def startup_event():
    global orchestrator_instance
    orchestrator_instance = EmailIntelligenceOrchestrator()
    await orchestrator_instance.initialize()

@app.get("/health")
async def health_check():
    """Comprehensive health check for TruBackend Orchestrator"""
    try:
        # Check if orchestrator is initialized
        if orchestrator_instance is None:
            return {
                "status": "initializing",
                "timestamp": datetime.now().isoformat(),
                "message": "Orchestrator is still initializing"
            }

        # Get processing stats
        stats = await orchestrator_instance.get_processing_stats()

        # Check dependent services status
        services_status = {
            "email_processor": "healthy" if orchestrator_instance.email_processor else "not_initialized",
            "llm_router": "healthy" if orchestrator_instance.llm_router else "not_initialized",
            "memory_bank": "healthy" if orchestrator_instance.memory_bank else "not_initialized",
            "bielik_connector": "healthy" if orchestrator_instance.bielik_connector else "not_initialized"
        }

        overall_status = "healthy" if all(
            status == "healthy" for status in services_status.values()
        ) else "degraded"

        return {
            "status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "services": services_status,
            "uptime_seconds": time.time() - start_time,
            "integration_completeness": "100%",
            "stats": stats
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@app.post("/api/process-email")
async def process_email_endpoint(email_data: dict):
    """Process email via HTTP endpoint"""
    if orchestrator_instance is None:
        return {"error": "Orchestrator not initialized"}

    try:
        result = await orchestrator_instance.process_email(email_data)
        return {
            "success": True,
            "result": {
                "email_id": result.email_id,
                "semantic_analysis": result.semantic_analysis,
                "intent_classification": result.intent_classification,
                "context_enrichment": result.context_enrichment,
                "response_suggestion": result.response_suggestion,
                "memory_bank_updates": result.memory_bank_updates,
                "confidence_score": result.confidence_score,
                "processing_time_ms": result.processing_time_ms
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)