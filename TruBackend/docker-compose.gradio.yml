# 🚀 TruBackend with Gradio Interface - Docker Compose

version: '3.8'

services:
  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trubackend-redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TruBackend Simplified
  trubackend-simplified:
    build:
      context: .
      dockerfile: Dockerfile.simplified
    container_name: trubackend-simplified
    ports:
      - "8000:8000"
    environment:
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
    volumes:
      - ./logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Gradio Interface
  gradio-interface:
    build:
      context: .
      dockerfile: Dockerfile.gradio
    container_name: trubackend-gradio
    ports:
      - "7860:7860"
    environment:
      - TRUBACKEND_API_URL=http://trubackend-simplified:8000
      - LOG_LEVEL=INFO
    depends_on:
      trubackend-simplified:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  redis_data:

networks:
  default:
    name: trubackend-network