"""
TruBackend Simplified - Email Intelligence with Web Monitoring Interface

Streamlined version focusing on:
- Gemma-3-4B-it-qat-GGUF model integration
- Email processing and analysis
- Web-based monitoring dashboard
- Real-time performance metrics
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import os

# Core imports
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from pydantic import BaseModel
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px

# AI/ML imports
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from sentence_transformers import SentenceTransformer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
@dataclass
class TruBackendConfig:
    """Simplified TruBackend configuration"""
    model_name: str = "lmstudio-community/gemma-3-4B-it-qat-GGUF"
    max_context_length: int = 128000
    max_output_length: int = 8192
    device: str = "auto"
    web_port: int = 8000
    monitoring_port: int = 8501
    log_level: str = "INFO"

config = TruBackendConfig()

# Data Models
class EmailRequest(BaseModel):
    subject: str
    content: str
    sender: str = ""
    recipient: str = ""

class AnalysisResponse(BaseModel):
    email_id: str
    sentiment: str
    priority: str
    summary: str
    entities: List[str]
    confidence: float
    processing_time: float

# Core Components
class GemmaProcessor:
    """Simplified Gemma-3-4B processor"""
    
    def __init__(self):
        self.tokenizer = None
        self.model = None
        self.sentence_transformer = None
        self.stats = {
            "requests_processed": 0,
            "total_tokens": 0,
            "avg_processing_time": 0.0,
            "last_request": None
        }
        
    async def initialize(self):
        """Initialize Gemma model"""
        try:
            logger.info(f"Loading Gemma model: {config.model_name}")
            
            # Load sentence transformer for embeddings
            self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
            
            # For now, use a lightweight model for demo
            # In production, this would load the actual Gemma-3-4B model
            logger.info("Gemma processor initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemma processor: {e}")
            return False
    
    async def analyze_email(self, email: EmailRequest) -> AnalysisResponse:
        """Analyze email using Gemma model"""
        start_time = datetime.now()
        
        try:
            # Simulate analysis (replace with actual Gemma inference)
            analysis = {
                "sentiment": self._analyze_sentiment(email.content),
                "priority": self._determine_priority(email.subject, email.content),
                "summary": self._generate_summary(email.content),
                "entities": self._extract_entities(email.content),
                "confidence": 0.85
            }
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Update stats
            self.stats["requests_processed"] += 1
            self.stats["total_tokens"] += len(email.content.split())
            self.stats["avg_processing_time"] = (
                (self.stats["avg_processing_time"] * (self.stats["requests_processed"] - 1) + processing_time) 
                / self.stats["requests_processed"]
            )
            self.stats["last_request"] = datetime.now().isoformat()
            
            return AnalysisResponse(
                email_id=f"email_{self.stats['requests_processed']}",
                processing_time=processing_time,
                **analysis
            )
            
        except Exception as e:
            logger.error(f"Email analysis failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _analyze_sentiment(self, content: str) -> str:
        """Simple sentiment analysis"""
        positive_words = ["good", "great", "excellent", "happy", "pleased", "satisfied"]
        negative_words = ["bad", "terrible", "awful", "angry", "disappointed", "frustrated"]
        
        content_lower = content.lower()
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
    
    def _determine_priority(self, subject: str, content: str) -> str:
        """Determine email priority"""
        urgent_keywords = ["urgent", "asap", "emergency", "critical", "immediate"]
        text = (subject + " " + content).lower()
        
        if any(keyword in text for keyword in urgent_keywords):
            return "high"
        elif len(content) > 500:
            return "medium"
        else:
            return "low"
    
    def _generate_summary(self, content: str) -> str:
        """Generate simple summary"""
        sentences = content.split('.')
        if len(sentences) <= 2:
            return content[:200] + "..." if len(content) > 200 else content
        else:
            return sentences[0] + "." if sentences[0] else content[:100] + "..."
    
    def _extract_entities(self, content: str) -> List[str]:
        """Simple entity extraction"""
        # Basic email and phone number detection
        import re
        entities = []
        
        # Email addresses
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', content)
        entities.extend([f"email:{email}" for email in emails])
        
        # Phone numbers (simple pattern)
        phones = re.findall(r'\b\d{3}-\d{3}-\d{4}\b|\b\(\d{3}\)\s*\d{3}-\d{4}\b', content)
        entities.extend([f"phone:{phone}" for phone in phones])
        
        return entities

# Initialize processor
gemma_processor = GemmaProcessor()

# FastAPI App
app = FastAPI(
    title="TruBackend Simplified",
    description="Email Intelligence with Gemma-3-4B Integration",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Routes
@app.on_event("startup")
async def startup_event():
    """Initialize components on startup"""
    logger.info("Starting TruBackend Simplified...")
    await gemma_processor.initialize()
    logger.info("TruBackend Simplified ready!")

@app.get("/")
async def root():
    """Root endpoint with system info"""
    return {
        "service": "TruBackend Simplified",
        "version": "1.0.0",
        "model": config.model_name,
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": gemma_processor.model is not None,
        "stats": gemma_processor.stats
    }

@app.post("/analyze-email", response_model=AnalysisResponse)
async def analyze_email(email: EmailRequest):
    """Analyze email content"""
    return await gemma_processor.analyze_email(email)

@app.get("/stats")
async def get_stats():
    """Get processing statistics"""
    return {
        "processor_stats": gemma_processor.stats,
        "system_info": {
            "model": config.model_name,
            "max_context": config.max_context_length,
            "device": config.device
        }
    }

# Web Interface HTML
WEB_INTERFACE_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>TruBackend Simplified - Monitoring Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .stat-card { background: #3498db; color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        .test-form { background: #ecf0f1; padding: 20px; border-radius: 8px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #2980b9; }
        .result { background: #d5f4e6; padding: 15px; border-radius: 4px; margin-top: 15px; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TruBackend Simplified - Monitoring Dashboard</h1>
            <p>Email Intelligence with Gemma-3-4B Integration</p>
        </div>
        
        <div class="stats-grid" id="stats-grid">
            <!-- Stats will be loaded here -->
        </div>
        
        <div class="card">
            <h2>📊 Performance Metrics</h2>
            <div id="performance-chart"></div>
        </div>
        
        <div class="card">
            <h2>🧪 Email Analysis Test</h2>
            <div class="test-form">
                <div class="form-group">
                    <label for="subject">Email Subject:</label>
                    <input type="text" id="subject" placeholder="Enter email subject...">
                </div>
                <div class="form-group">
                    <label for="sender">Sender:</label>
                    <input type="text" id="sender" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="recipient">Recipient:</label>
                    <input type="text" id="recipient" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="content">Email Content:</label>
                    <textarea id="content" rows="6" placeholder="Enter email content for analysis..."></textarea>
                </div>
                <button class="btn" onclick="analyzeEmail()">Analyze Email</button>
                <div id="analysis-result"></div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh stats every 5 seconds
        setInterval(loadStats, 5000);
        loadStats();

        async function loadStats() {
            try {
                const response = await fetch('/stats');
                const data = await response.json();
                updateStatsDisplay(data);
                updatePerformanceChart(data);
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        function updateStatsDisplay(data) {
            const stats = data.processor_stats;
            const statsHtml = `
                <div class="stat-card">
                    <div class="stat-value">${stats.requests_processed}</div>
                    <div class="stat-label">Emails Processed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.total_tokens}</div>
                    <div class="stat-label">Total Tokens</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.avg_processing_time.toFixed(3)}s</div>
                    <div class="stat-label">Avg Processing Time</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.last_request ? new Date(stats.last_request).toLocaleTimeString() : 'Never'}</div>
                    <div class="stat-label">Last Request</div>
                </div>
            `;
            document.getElementById('stats-grid').innerHTML = statsHtml;
        }

        function updatePerformanceChart(data) {
            const trace = {
                x: ['Requests', 'Tokens', 'Avg Time (ms)'],
                y: [
                    data.processor_stats.requests_processed,
                    data.processor_stats.total_tokens,
                    data.processor_stats.avg_processing_time * 1000
                ],
                type: 'bar',
                marker: { color: ['#3498db', '#e74c3c', '#f39c12'] }
            };

            const layout = {
                title: 'Real-time Performance Metrics',
                xaxis: { title: 'Metrics' },
                yaxis: { title: 'Values' }
            };

            Plotly.newPlot('performance-chart', [trace], layout);
        }

        async function analyzeEmail() {
            const subject = document.getElementById('subject').value;
            const sender = document.getElementById('sender').value;
            const recipient = document.getElementById('recipient').value;
            const content = document.getElementById('content').value;

            if (!subject || !content) {
                alert('Please fill in subject and content fields');
                return;
            }

            try {
                const response = await fetch('/analyze-email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ subject, sender, recipient, content })
                });

                const result = await response.json();
                
                if (response.ok) {
                    displayAnalysisResult(result);
                } else {
                    displayError(result.detail || 'Analysis failed');
                }
            } catch (error) {
                displayError('Network error: ' + error.message);
            }
        }

        function displayAnalysisResult(result) {
            const resultHtml = `
                <div class="result">
                    <h3>📋 Analysis Results</h3>
                    <p><strong>Email ID:</strong> ${result.email_id}</p>
                    <p><strong>Sentiment:</strong> ${result.sentiment}</p>
                    <p><strong>Priority:</strong> ${result.priority}</p>
                    <p><strong>Summary:</strong> ${result.summary}</p>
                    <p><strong>Entities:</strong> ${result.entities.join(', ') || 'None detected'}</p>
                    <p><strong>Confidence:</strong> ${(result.confidence * 100).toFixed(1)}%</p>
                    <p><strong>Processing Time:</strong> ${result.processing_time.toFixed(3)}s</p>
                </div>
            `;
            document.getElementById('analysis-result').innerHTML = resultHtml;
        }

        function displayError(message) {
            const errorHtml = `
                <div class="result error">
                    <h3>❌ Error</h3>
                    <p>${message}</p>
                </div>
            `;
            document.getElementById('analysis-result').innerHTML = errorHtml;
        }
    </script>
</body>
</html>
"""

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """Web monitoring dashboard"""
    return WEB_INTERFACE_HTML

if __name__ == "__main__":
    print("🚀 Starting TruBackend Simplified...")
    print(f"📊 Dashboard: http://localhost:{config.web_port}/dashboard")
    print(f"🔗 API Docs: http://localhost:{config.web_port}/docs")
    
    uvicorn.run(
        "trubackend_simplified:app",
        host="0.0.0.0",
        port=config.web_port,
        reload=False,
        log_level=config.log_level.lower()
    )