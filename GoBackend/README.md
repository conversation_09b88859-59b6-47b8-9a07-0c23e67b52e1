# GoBackend HVAC - Advanced HVAC CRM System

A powerful, AI-enhanced HVAC CRM backend built with Go, featuring advanced email processing, AI-powered analysis, and comprehensive customer management.

## 🚀 Features

### Core HVAC CRM
- **Customer Management**: Complete customer lifecycle management
- **Job Scheduling**: Advanced job tracking and scheduling
- **Quote Generation**: AI-powered quote generation and management
- **Service History**: Comprehensive service tracking

### AI-Powered Features
- **Email Analysis**: Automatic sentiment analysis and categorization
- **Smart Quotes**: AI-generated service quotes
- **Customer Insights**: AI-driven customer behavior analysis
- **Predictive Maintenance**: ML-based maintenance scheduling

### Email Integration
- **SMTP Support**: Full email sending capabilities
- **Email Analysis**: Automatic email categorization and priority assignment
- **Template System**: Dynamic email templates
- **Bulk Operations**: Mass email campaigns

### Memory & Context Management
- **Persistent Memory**: Long-term customer context storage
- **Session Management**: Temporary session data
- **Search Capabilities**: Advanced memory search and retrieval
- **Auto-Cleanup**: Automatic expired memory cleanup

## 🛠 Technology Stack

- **Language**: Go 1.24+
- **Web Framework**: Gin
- **Database**: PostgreSQL with GORM
- **AI/ML**: Multiple providers (OpenAI, Anthropic, Ollama, HuggingFace)
- **Email**: go-mail with SMTP support
- **Memory**: Advanced memory management with fmstephe/memorymanager
- **Containerization**: Docker & Docker Compose

## 📦 Installation

### Prerequisites
- Go 1.24+
- Docker & Docker Compose
- PostgreSQL (if running locally)

### Quick Start with Docker

1. **Clone the repository**
```bash
git clone <repository-url>
cd GoBackend
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start services**
```bash
docker-compose up -d
```

4. **Verify installation**
```bash
curl http://localhost:8080/api/v1/health
```### Local Development

1. **Install dependencies**
```bash
go mod download
```

2. **Set up database**
```bash
# Start PostgreSQL
docker run -d --name postgres \
  -e POSTGRES_DB=hvac_go \
  -e POSTGRES_USER=hvac_user \
  -e POSTGRES_PASSWORD=hvac_password \
  -p 5432:5432 postgres:15-alpine
```

3. **Run the application**
```bash
go run cmd/main.go
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgres://localhost/hvac_go?sslmode=disable` |
| `PORT` | Server port | `8080` |
| `OPENAI_API_KEY` | OpenAI API key | - |
| `ANTHROPIC_API_KEY` | Anthropic API key | - |
| `OLLAMA_URL` | Ollama server URL | `http://localhost:11434` |
| `SMTP_HOST` | SMTP server host | `localhost` |
| `SMTP_PORT` | SMTP server port | `587` |

### AI Provider Setup

Configure at least one AI provider:

**OpenAI**
```bash
export OPENAI_API_KEY=your_openai_key
```

**Anthropic**
```bash
export ANTHROPIC_API_KEY=your_anthropic_key
```

**Ollama (Local)**
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull a model
ollama pull llama2
```

## 📚 API Documentation

### Health Check
```bash
GET /api/v1/health
```

### Customer Management
```bash
# Create customer
POST /api/v1/hvac/customers
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "555-0123",
  "address": "123 Main St"
}

# List customers
GET /api/v1/hvac/customers
```