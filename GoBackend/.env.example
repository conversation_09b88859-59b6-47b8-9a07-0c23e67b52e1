# Database
DATABASE_URL=postgres://hvac_user:hvac_password@localhost:5432/hvac_go?sslmode=disable

# Server
PORT=8080
ENVIRONMENT=development

# JWT
JWT_SECRET=your-super-secret-jwt-key

# Email/SMTP
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=

# AI Providers (configure at least one)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
OLLAMA_URL=http://localhost:11434
HUGGINGFACE_TOKEN=

# Memory
MEMORY_RETENTION_DAYS=30