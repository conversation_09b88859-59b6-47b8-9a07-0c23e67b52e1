# GoBackend HVAC - Makefile

.PHONY: help build run test clean docker-build docker-run docker-stop deps

# Default target
help:
	@echo "GoBackend HVAC - Available commands:"
	@echo ""
	@echo "  make build       - Build the application"
	@echo "  make run         - Run the application locally"
	@echo "  make test        - Run tests"
	@echo "  make clean       - Clean build artifacts"
	@echo "  make deps        - Download dependencies"
	@echo ""
	@echo "  make docker-build - Build Docker image"
	@echo "  make docker-run   - Run with Docker Compose"
	@echo "  make docker-stop  - Stop Docker containers"
	@echo ""
	@echo "  make setup       - Initial setup (copy .env.example)"

# Build the application
build:
	@echo "🔨 Building GoBackend HVAC..."
	go build -o main ./cmd/main.go

# Run the application locally
run: build
	@echo "🚀 Starting GoBackend HVAC..."
	./main

# Run tests
test:
	@echo "🧪 Running tests..."
	go test ./...

# Clean build artifacts
clean:
	@echo "🧹 Cleaning..."
	rm -f main
	go clean

# Download dependencies
deps:
	@echo "📦 Downloading dependencies..."
	go mod download
	go mod tidy

# Setup environment
setup:
	@echo "⚙️  Setting up environment..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "📝 .env file created. Please edit it with your configuration."; \
	else \
		echo "✅ .env file already exists."; \
	fi

# Docker commands
docker-build:
	@echo "🐳 Building Docker image..."
	docker-compose build

docker-run:
	@echo "🐳 Starting with Docker..."
	./scripts/docker-start.sh

docker-stop:
	@echo "🛑 Stopping Docker containers..."
	docker-compose down