# GoBackend HVAC - API Examples

## Health Check

```bash
curl http://localhost:8080/api/v1/health
```

## Customer Management

### Create Customer
```bash
curl -X POST http://localhost:8080/api/v1/hvac/customers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "555-0123",
    "address": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "zip_code": "12345"
  }'
```

### List Customers
```bash
curl http://localhost:8080/api/v1/hvac/customers
```

## Job Management

### Create Job
```bash
curl -X POST http://localhost:8080/api/v1/hvac/jobs \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "title": "AC Repair",
    "description": "Fix broken air conditioning unit",
    "priority": "high"
  }'
```

### List Jobs
```bash
curl http://localhost:8080/api/v1/hvac/jobs
```

## Quote Management

### Create Quote
```bash
curl -X POST http://localhost:8080/api/v1/hvac/quotes \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "title": "AC Repair Quote",
    "description": "Repair and maintenance of AC unit",
    "amount": 299.99
  }'
```

### List Quotes
```bash
curl http://localhost:8080/api/v1/hvac/quotes
```## Email Management

### Send Email
```bash
curl -X POST http://localhost:8080/api/v1/email/send \
  -H "Content-Type: application/json" \
  -d '{
    "from": "<EMAIL>",
    "to": "<EMAIL>",
    "subject": "Service Appointment Confirmation",
    "body": "Your HVAC service appointment is confirmed for tomorrow at 2 PM."
  }'
```

### Analyze Email
```bash
curl http://localhost:8080/api/v1/email/analyze/1
```

### List Emails
```bash
curl http://localhost:8080/api/v1/email/list
```

## AI Features

### AI Chat
```bash
curl -X POST http://localhost:8080/api/v1/ai/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the common causes of AC not cooling properly?"
  }'
```

### AI Text Analysis
```bash
curl -X POST http://localhost:8080/api/v1/ai/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "text": "My air conditioner is making loud noises and not cooling. This is urgent!"
  }'
```

### List AI Models
```bash
curl http://localhost:8080/api/v1/ai/models
```

## Memory Management

### Store Memory
```bash
curl -X POST http://localhost:8080/api/v1/memory/store \
  -H "Content-Type: application/json" \
  -d '{
    "key": "customer_preferences_123",
    "content": "Customer prefers morning appointments, has 2 dogs",
    "type": "customer",
    "context": "Customer ID 123 preferences"
  }'
```

### Retrieve Memory
```bash
curl http://localhost:8080/api/v1/memory/retrieve/customer_preferences_123
```

### Search Memory
```bash
curl "http://localhost:8080/api/v1/memory/search?q=morning&type=customer"
```