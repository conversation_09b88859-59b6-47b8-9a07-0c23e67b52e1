Oto projekty w języku Go dostępne na GitHubie, które mogą zastąpić funkcjonalności Pythonowe w systemie TruBackend:
https://github.com/wneessen/go-mail
https://github.com/kedark3/email-analyzer
https://github.com/teilomillet/gollm
https://github.com/knights-analytics/hugot
https://github.com/space-cadet/memory-bank
https://github.com/golang-jwt/jwt
1. Przetwarzanie i integracja emaili
go-mail (wneessen/go-mail)
Zastosowanie: Moduł akwizycji i wysyłania emaili

Kluczowe funkcje:

Pełna obsługa MIME i multipart

Integracja z SMTP (w tym TLS)

Generowanie strukturalnych wiadomości

Wsparcie dla załączników i nagłówków

email-analyzer (kedark3/email-analyzer)
Zastosowanie: Analiza nagłówków i treści emaili

Funkcjonalności:

Parsowanie struktury emaili

Ekstrakcja metadanych (From/To/Subject)

Integracja z REST API

Deployment w Kubernetes

2. Integracja z modelami AI/ML
gollm (teilomillet/gollm)
Zastosowanie: Langchain Automation i Executive AI Assistant

Możliwości:

Unified API dla OpenAI, Anthropic, Ollama

Prompt engineering i optymalizacja

Structured output validation

Wsparcie dla RAG (Retrieval-Augmented Generation)

hugot (knights-analytics/hugot)
Zastosowanie: Integracja z modelami Hugging Face

Cechy:

Wsparcie dla transformerów przez ONNX

Inferencja modeli NLP

Kompatybilność z formatami PyTorch

3. Zarządzanie pamięcią i kontekstem
memory-bank (space-cadet/memory-bank)
Zastosowanie: Memory Bank System

Funkcje:

Przechowywanie kontekstu klienta

Integracja z kalendarzem

Mechanizmy progressive loading

Obsługa wielowątkowości

4. Deployment i infrastruktura
Narzędzie	Zastosowanie	Kluczowe cechy
Docker	Konteneryzacja usług	Obrazy dla poszczególnych modułów
Kubernetes	Orchestracja	Skalowanie automatyczne
Prometheus	Monitoring	Zbieranie metryk wydajnościowych
Grafana	Wizualizacja danych	Pulpity nawigacyjne dla HVAC analytics
5. Bezpieczeństwo i API
jwt-go (https://github.com/golang-jwt/jwt)
Zastosowanie: Autentykacja i autoryzacja

Funkcje:

Generowanie tokenów JWT

Integracja z RBAC

