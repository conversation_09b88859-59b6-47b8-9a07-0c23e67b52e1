package config

import (
	"os"
	"strconv"
)

type Config struct {
	// Database
	DatabaseURL string
	
	// Email
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	
	// AI Models
	OpenAIAPIKey     string
	AnthropicAPIKey  string
	OllamaURL        string
	HuggingFaceToken string
	
	// Server
	Port        string
	Environment string
	
	// JWT
	JWTSecret string
	
	// Memory
	MemoryRetentionDays int
}

func Load() *Config {
	return &Config{
		DatabaseURL:         getEnv("DATABASE_URL", "postgres://localhost/hvac_go?sslmode=disable"),
		SMTPHost:           getEnv("SMTP_HOST", "localhost"),
		SMTPPort:           getEnvAsInt("SMTP_PORT", 587),
		SMTPUsername:       getEnv("SMTP_USERNAME", ""),
		SMTPPassword:       getEnv("SMTP_PASSWORD", ""),
		OpenAIAPIKey:       getEnv("OPENAI_API_KEY", ""),
		AnthropicAPIKey:    getEnv("ANTHROPIC_API_KEY", ""),
		OllamaURL:          getEnv("OLLAMA_URL", "http://localhost:11434"),
		HuggingFaceToken:   getEnv("HUGGINGFACE_TOKEN", ""),
		Port:               getEnv("PORT", "8080"),
		Environment:        getEnv("ENVIRONMENT", "development"),
		JWTSecret:          getEnv("JWT_SECRET", "your-secret-key"),
		MemoryRetentionDays: getEnvAsInt("MEMORY_RETENTION_DAYS", 30),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}