package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handlers) StoreMemory(c *gin.Context) {
	var request struct {
		Key     string `json:"key" binding:"required"`
		Content string `json:"content" binding:"required"`
		Type    string `json:"type" binding:"required"`
		Context string `json:"context"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := h.memoryService.Store(request.Key, request.Content, request.Type, request.Context)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Memory stored successfully",
		"key": request.Key,
	})
}

func (h *Handlers) RetrieveMemory(c *gin.Context) {
	key := c.Param("id")

	memory, err := h.memoryService.Retrieve(key)
	if err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{"error": "Memory not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"memory": memory,
	})
}

func (h *Handlers) SearchMemory(c *gin.Context) {
	query := c.Query("q")
	memoryType := c.Query("type")

	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Query parameter 'q' is required"})
		return
	}

	memories, err := h.memoryService.Search(query, memoryType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"query": query,
		"memories": memories,
	})
}