package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gobackend-hvac/internal/models"
)

func (h *Handlers) CreateCustomer(c *gin.Context) {
	var customer models.Customer
	if err := c.ShouldBindJSON(&customer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// In real implementation, save to database
	customer.ID = 1
	customer.CreatedAt = time.Now()
	customer.UpdatedAt = time.Now()

	c.JSON(http.StatusCreated, gin.H{
		"message": "Customer created successfully",
		"customer": customer,
	})
}

func (h *Handlers) ListCustomers(c *gin.Context) {
	// Mock data - in real implementation, fetch from database
	customers := []models.Customer{
		{
			ID:      1,
			Name:    "<PERSON>",
			Email:   "<EMAIL>",
			Phone:   "555-0123",
			Address: "123 Main St",
			City:    "Anytown",
			State:   "CA",
			ZipCode: "12345",
		},
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"customers": customers,
	})
}

func (h *Handlers) CreateJob(c *gin.Context) {
	var job models.Job
	if err := c.ShouldBindJSON(&job); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	job.ID = 1
	job.CreatedAt = time.Now()
	job.UpdatedAt = time.Now()

	c.JSON(http.StatusCreated, gin.H{
		"message": "Job created successfully",
		"job": job,
	})
}

func (h *Handlers) ListJobs(c *gin.Context) {
	jobs := []models.Job{
		{
			ID:          1,
			CustomerID:  1,
			Title:       "AC Repair",
			Description: "Fix broken air conditioning unit",
			Status:      "pending",
			Priority:    "high",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"jobs": jobs,
	})
}

func (h *Handlers) CreateQuote(c *gin.Context) {
	var quote models.Quote
	if err := c.ShouldBindJSON(&quote); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	quote.ID = 1
	quote.CreatedAt = time.Now()
	quote.UpdatedAt = time.Now()

	c.JSON(http.StatusCreated, gin.H{
		"message": "Quote created successfully",
		"quote": quote,
	})
}

func (h *Handlers) ListQuotes(c *gin.Context) {
	quotes := []models.Quote{
		{
			ID:          1,
			CustomerID:  1,
			Title:       "AC Repair Quote",
			Description: "Repair and maintenance of AC unit",
			Amount:      299.99,
			Status:      "draft",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"quotes": quotes,
	})
}