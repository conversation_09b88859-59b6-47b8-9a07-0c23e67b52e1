package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gobackend-hvac/internal/services"
	"gobackend-hvac/internal/models"
)

type Handlers struct {
	emailService  *services.EmailService
	aiService     *services.AIService
	memoryService *services.MemoryService
}

func NewHandlers(emailService *services.EmailService, aiService *services.AIService, memoryService *services.MemoryService) *Handlers {
	return &Handlers{
		emailService:  emailService,
		aiService:     aiService,
		memoryService: memoryService,
	}
}

// Health check endpoint
func (h *Handlers) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "healthy",
		"service": "GoBackend HVAC",
		"version": "1.0.0",
	})
}

// Email handlers
func (h *Handlers) SendEmail(c *gin.Context) {
	var email models.Email
	if err := c.ShouldBindJSON(&email); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.emailService.SendEmail(&email); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Email sent successfully",
		"email": email,
	})
}

func (h *Handlers) AnalyzeEmail(c *gin.Context) {
	id := c.Param("id")
	
	// In a real implementation, fetch email from database
	email := &models.Email{
		Subject: "Sample email",
		Body:    "This is a sample email for analysis",
	}

	if err := h.emailService.AnalyzeEmail(email); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id": id,
		"analysis": email,
	})
}

func (h *Handlers) ListEmails(c *gin.Context) {
	// Mock data - in real implementation, fetch from database
	emails := []models.Email{
		{
			ID:       1,
			From:     "<EMAIL>",
			To:       "<EMAIL>",
			Subject:  "AC Repair Request",
			Body:     "My AC is not working properly",
			Status:   "received",
			Sentiment: "neutral",
			Category: "service_request",
			Priority: "medium",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"emails": emails,
	})
}