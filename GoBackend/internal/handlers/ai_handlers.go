package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *Handlers) AIChat(c *gin.Context) {
	var request struct {
		Message string `json:"message" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.aiService.Chat(c.Request.Context(), request.Message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"message": request.Message,
		"response": response,
	})
}

func (h *Handlers) AIAnalyze(c *gin.Context) {
	var request struct {
		Text string `json:"text" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	analysis, err := h.aiService.AnalyzeText(c.Request.Context(), request.Text)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"text": request.Text,
		"analysis": analysis,
	})
}

func (h *Handlers) ListAIModels(c *gin.Context) {
	models := []gin.H{
		{"name": "gpt-4", "provider": "openai", "status": "active"},
		{"name": "claude-3-sonnet", "provider": "anthropic", "status": "active"},
		{"name": "llama2", "provider": "ollama", "status": "active"},
	}

	c.JSON(http.StatusOK, gin.H{
		"models": models,
	})
}