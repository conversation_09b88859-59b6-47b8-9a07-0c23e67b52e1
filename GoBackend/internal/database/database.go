package database

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	
	"gobackend-hvac/internal/models"
)

// Initialize creates and configures the database connection
func Initialize(databaseURL string) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&models.Customer{},
		&models.Job{},
		&models.Quote{},
		&models.Email{},
		&models.Memory{},
		&models.AIModel{},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	log.Println("Database connected and migrated successfully")
	return db, nil
}

// SeedData populates the database with initial data
func SeedData(db *gorm.DB) error {
	// Seed AI Models
	aiModels := []models.AIModel{
		{
			Name:     "gpt-4",
			Provider: "openai",
			Type:     "chat",
			Endpoint: "https://api.openai.com/v1/chat/completions",
			Status:   "active",
			Config:   `{"max_tokens": 4096, "temperature": 0.7}`,
		},
		{
			Name:     "claude-3-sonnet",
			Provider: "anthropic",
			Type:     "chat",
			Endpoint: "https://api.anthropic.com/v1/messages",
			Status:   "active",
			Config:   `{"max_tokens": 4096, "temperature": 0.7}`,
		},
	}
	
	for _, model := range aiModels {
		var existing models.AIModel
		if err := db.Where("name = ?", model.Name).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&model).Error; err != nil {
					return fmt.Errorf("failed to seed AI model %s: %w", model.Name, err)
				}
			}
		}
	}

	log.Println("Database seeded successfully")
	return nil
}