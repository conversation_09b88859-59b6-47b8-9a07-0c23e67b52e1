package services

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	"gobackend-hvac/internal/models"
)

type MemoryService struct {
	db *gorm.DB
}

func NewMemoryService(db *gorm.DB) *MemoryService {
	return &MemoryService{
		db: db,
	}
}

func (s *MemoryService) Store(key, content, memoryType, context string) error {
	memory := &models.Memory{
		Key:     key,
		Content: content,
		Type:    memoryType,
		Context: context,
	}

	// Set expiration based on type
	switch memoryType {
	case "session":
		expiry := time.Now().Add(24 * time.Hour)
		memory.ExpiresAt = &expiry
	case "customer":
		expiry := time.Now().Add(365 * 24 * time.Hour)
		memory.ExpiresAt = &expiry
	case "permanent":
		// No expiration
	default:
		expiry := time.Now().Add(30 * 24 * time.Hour)
		memory.ExpiresAt = &expiry
	}

	return s.db.Create(memory).Error
}

func (s *MemoryService) Retrieve(key string) (*models.Memory, error) {
	var memory models.Memory
	err := s.db.Where("key = ? AND (expires_at IS NULL OR expires_at > ?)", 
		key, time.Now()).First(&memory).Error
	
	if err != nil {
		return nil, fmt.Errorf("memory not found: %w", err)
	}

	return &memory, nil
}func (s *MemoryService) Search(query string, memoryType string) ([]models.Memory, error) {
	var memories []models.Memory
	
	db := s.db.Where("(expires_at IS NULL OR expires_at > ?)", time.Now())
	
	if memoryType != "" {
		db = db.Where("type = ?", memoryType)
	}
	
	// Simple text search - in production, use full-text search or vector similarity
	db = db.Where("content ILIKE ? OR context ILIKE ?", "%"+query+"%", "%"+query+"%")
	
	err := db.Find(&memories).Error
	if err != nil {
		return nil, fmt.Errorf("failed to search memories: %w", err)
	}

	return memories, nil
}

func (s *MemoryService) CleanExpired() error {
	return s.db.Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Delete(&models.Memory{}).Error
}