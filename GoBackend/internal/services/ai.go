package services

import (
	"context"
	"fmt"

	"github.com/teilomillet/gollm"
	"gobackend-hvac/internal/config"
	"gobackend-hvac/internal/models"
)

type AIService struct {
	config *config.Config
	llm    gollm.LLM
}

func NewAIService(cfg *config.Config) *AIService {
	var llm gollm.LLM
	var err error

	// Initialize LLM based on available API keys
	if cfg.OpenAIAPIKey != "" {
		llm, err = gollm.NewLLM(
			gollm.SetProvider("openai"),
			gollm.SetModel("gpt-4"),
			gollm.SetAPIKey(cfg.OpenAIAPIKey),
		)
	} else if cfg.AnthropicAPIKey != "" {
		llm, err = gollm.NewLLM(
			gollm.SetProvider("anthropic"),
			gollm.SetModel("claude-3-sonnet-20240229"),
			gollm.SetAPIKey(cfg.AnthropicAPIKey),
		)
	} else if cfg.OllamaURL != "" {
		llm, err = gollm.NewLLM(
			gollm.SetProvider("ollama"),
			gollm.SetModel("llama2"),
			gollm.SetEndpoint(cfg.OllamaURL),
		)
	} else {
		panic("No AI provider configured")
	}

	if err != nil {
		panic(fmt.Sprintf("Failed to initialize AI service: %v", err))
	}

	return &AIService{
		config: cfg,
		llm:    llm,
	}
}

func (s *AIService) Chat(ctx context.Context, message string) (string, error) {
	prompt := gollm.NewPrompt(message)
	
	response, err := s.llm.Generate(ctx, prompt)
	if err != nil {
		return "", fmt.Errorf("failed to generate AI response: %w", err)
	}

	return response, nil
}

func (s *AIService) AnalyzeText(ctx context.Context, text string) (*models.Email, error) {
	prompt := gollm.NewPrompt(fmt.Sprintf(`
Analyze the following HVAC-related text and provide:
1. Sentiment (positive, negative, neutral)
2. Category (quote_request, emergency, service_request, general)
3. Priority (urgent, high, medium, low)
4. Key keywords (comma-separated)

Text: %s

Respond in JSON format:
{
  "sentiment": "",
  "category": "",
  "priority": "",
  "keywords": ""
}`, text))

	response, err := s.llm.Generate(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze text: %w", err)
	}

	// Parse JSON response and create Email model
	email := &models.Email{
		Body: text,
	}
	
	// Simple parsing - in production, use proper JSON parsing
	if contains(response, "positive") {
		email.Sentiment = "positive"
	} else if contains(response, "negative") {
		email.Sentiment = "negative"
	} else {
		email.Sentiment = "neutral"
	}

	return email, nil
}

func (s *AIService) GenerateQuote(ctx context.Context, description string) (string, error) {
	prompt := gollm.NewPrompt(fmt.Sprintf(`
As an HVAC professional, generate a detailed quote for the following service request:

%s

Include:
- Service description
- Estimated labor hours
- Material costs
- Total estimate
- Timeline

Format as a professional quote.`, description))

	response, err := s.llm.Generate(ctx, prompt)
	if err != nil {
		return "", fmt.Errorf("failed to generate quote: %w", err)
	}

	return response, nil
}