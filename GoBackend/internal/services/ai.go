package services

import (
	"context"
	"fmt"
	"log"
	"strings"

	"github.com/teilomillet/gollm"
	"gobackend-hvac/internal/config"
	"gobackend-hvac/internal/models"
)

type AIService struct {
	config *config.Config
	llm    gollm.LLM
}

func NewAIService(cfg *config.Config) *AIService {
	var llm gollm.LLM
	var err error

	// Initialize LLM based on available API keys
	if cfg.OpenAIAPIKey != "" {
		llm, err = gollm.NewLLM(
			gollm.SetProvider("openai"),
			gollm.SetModel("gpt-4"),
			gollm.SetAPIKey(cfg.OpenAIAPIKey),
		)
	} else if cfg.AnthropicAPIKey != "" {
		llm, err = gollm.NewLLM(
			gollm.SetProvider("anthropic"),
			gollm.SetModel("claude-3-sonnet-20240229"),
			gollm.SetAPIKey(cfg.AnthropicAPIKey),
		)
	} else if cfg.OllamaURL != "" {
		llm, err = gollm.NewLLM(
			gollm.SetProvider("ollama"),
			gollm.SetModel("llama2"),
			gollm.SetAPIKey(cfg.OllamaURL),
		)
	} else {
		// Demo mode - no AI provider configured
		log.Println("Warning: No AI provider configured. Running in demo mode.")
		return &AIService{
			config: cfg,
			llm:    nil, // Will use mock responses
		}
	}

	if err != nil {
		log.Printf("Warning: Failed to initialize AI service: %v", err)
		log.Println("Running AI service in demo mode...")
		return &AIService{
			config: cfg,
			llm:    nil, // Will use mock responses
		}
	}

	return &AIService{
		config: cfg,
		llm:    llm,
	}
}

func (s *AIService) Chat(ctx context.Context, message string) (string, error) {
	if s.llm == nil {
		// Demo mode response
		return fmt.Sprintf("Demo AI Response: I understand you're asking about '%s'. In a real deployment, this would be processed by an AI model like GPT-4, Claude, or Ollama.", message), nil
	}

	prompt := gollm.NewPrompt(message)
	
	response, err := s.llm.Generate(ctx, prompt)
	if err != nil {
		return "", fmt.Errorf("failed to generate AI response: %w", err)
	}

	return response, nil
}

func (s *AIService) AnalyzeText(ctx context.Context, text string) (*models.Email, error) {
	email := &models.Email{
		Body: text,
	}

	if s.llm == nil {
		// Demo mode - simple analysis
		textLower := strings.ToLower(text)
		if strings.Contains(textLower, "urgent") || strings.Contains(textLower, "emergency") {
			email.Sentiment = "negative"
			email.Category = "emergency"
			email.Priority = "urgent"
		} else if strings.Contains(textLower, "quote") || strings.Contains(textLower, "estimate") {
			email.Sentiment = "neutral"
			email.Category = "quote_request"
			email.Priority = "high"
		} else if strings.Contains(textLower, "noise") || strings.Contains(textLower, "not cooling") || strings.Contains(textLower, "broken") {
			email.Sentiment = "negative"
			email.Category = "service_request"
			email.Priority = "high"
		} else {
			email.Sentiment = "neutral"
			email.Category = "general"
			email.Priority = "medium"
		}
		email.Keywords = "demo, analysis, hvac"
		return email, nil
	}

	prompt := gollm.NewPrompt(fmt.Sprintf(`
Analyze the following HVAC-related text and provide:
1. Sentiment (positive, negative, neutral)
2. Category (quote_request, emergency, service_request, general)
3. Priority (urgent, high, medium, low)
4. Key keywords (comma-separated)

Text: %s

Respond in JSON format:
{
  "sentiment": "",
  "category": "",
  "priority": "",
  "keywords": ""
}`, text))

	response, err := s.llm.Generate(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze text: %w", err)
	}

	// Simple parsing - in production, use proper JSON parsing
	if contains(response, "positive") {
		email.Sentiment = "positive"
	} else if contains(response, "negative") {
		email.Sentiment = "negative"
	} else {
		email.Sentiment = "neutral"
	}

	return email, nil
}

func (s *AIService) GenerateQuote(ctx context.Context, description string) (string, error) {
	if s.llm == nil {
		// Demo mode response
		return fmt.Sprintf(`DEMO HVAC QUOTE

Service Request: %s

Service Description: Professional HVAC service
Estimated Labor: 2-4 hours
Material Costs: $150-300
Labor Costs: $200-400
Total Estimate: $350-700

Timeline: 1-2 business days

Note: This is a demo quote. In production, this would be generated by AI based on the specific service request.`, description), nil
	}

	prompt := gollm.NewPrompt(fmt.Sprintf(`
As an HVAC professional, generate a detailed quote for the following service request:

%s

Include:
- Service description
- Estimated labor hours
- Material costs
- Total estimate
- Timeline

Format as a professional quote.`, description))

	response, err := s.llm.Generate(ctx, prompt)
	if err != nil {
		return "", fmt.Errorf("failed to generate quote: %w", err)
	}

	return response, nil
}