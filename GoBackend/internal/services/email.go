package services

import (
	"fmt"
	"time"

	"github.com/wneessen/go-mail"
	"gobackend-hvac/internal/config"
	"gobackend-hvac/internal/models"
)

type EmailService struct {
	config *config.Config
	client *mail.Client
}

func NewEmailService(cfg *config.Config) *EmailService {
	client, err := mail.NewClient(cfg.SMTPHost, mail.WithPort(cfg.SMTPPort))
	if err != nil {
		panic(fmt.Sprintf("Failed to create email client: %v", err))
	}

	if cfg.SMTPUsername != "" && cfg.SMTPPassword != "" {
		client.SetSMTPAuth(mail.SMTPAuthPlain)
		client.SetUsername(cfg.SMTPUsername)
		client.SetPassword(cfg.SMTPPassword)
	}

	return &EmailService{
		config: cfg,
		client: client,
	}
}

func (s *EmailService) SendEmail(email *models.Email) error {
	msg := mail.NewMsg()
	
	if err := msg.From(email.From); err != nil {
		return fmt.Errorf("failed to set from address: %w", err)
	}
	
	if err := msg.To(email.To); err != nil {
		return fmt.Errorf("failed to set to address: %w", err)
	}
	
	msg.Subject(email.Subject)
	
	if email.HTMLBody != "" {
		msg.SetBodyString(mail.TypeTextHTML, email.HTMLBody)
	} else {
		msg.SetBodyString(mail.TypeTextPlain, email.Body)
	}

	if err := s.client.DialAndSend(msg); err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	now := time.Now()
	email.SentAt = &now
	email.Status = "sent"

	return nil
}

func (s *EmailService) AnalyzeEmail(email *models.Email) error {
	// Simple sentiment analysis based on keywords
	content := email.Subject + " " + email.Body
	
	// Basic sentiment analysis
	positiveWords := []string{"great", "excellent", "good", "satisfied", "happy", "pleased"}
	negativeWords := []string{"bad", "terrible", "awful", "disappointed", "angry", "frustrated"}
	
	positiveCount := 0
	negativeCount := 0
	
	for _, word := range positiveWords {
		if contains(content, word) {
			positiveCount++
		}
	}
	
	for _, word := range negativeWords {
		if contains(content, word) {
			negativeCount++
		}
	}
	
	if positiveCount > negativeCount {
		email.Sentiment = "positive"
	} else if negativeCount > positiveCount {
		email.Sentiment = "negative"
	} else {
		email.Sentiment = "neutral"
	}
	
	// Basic categorization
	if contains(content, "quote") || contains(content, "estimate") {
		email.Category = "quote_request"
		email.Priority = "high"
	} else if contains(content, "emergency") || contains(content, "urgent") {
		email.Category = "emergency"
		email.Priority = "urgent"
	} else if contains(content, "maintenance") || contains(content, "service") {
		email.Category = "service_request"
		email.Priority = "medium"
	} else {
		email.Category = "general"
		email.Priority = "low"
	}
	
	return nil
}

func contains(text, word string) bool {
	return len(text) > 0 && len(word) > 0 && 
		   (text == word || 
		    (len(text) > len(word) && 
		     (text[:len(word)] == word || 
		      text[len(text)-len(word):] == word)))
}