package models

import (
	"time"
	"gorm.io/gorm"
)

// Customer represents an HVAC customer
type Customer struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null"`
	Email       string    `json:"email" gorm:"unique;not null"`
	Phone       string    `json:"phone"`
	Address     string    `json:"address"`
	City        string    `json:"city"`
	State       string    `json:"state"`
	ZipCode     string    `json:"zip_code"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	Jobs        []Job     `json:"jobs,omitempty" gorm:"foreignKey:CustomerID"`
	Quotes      []Quote   `json:"quotes,omitempty" gorm:"foreignKey:CustomerID"`
}

// Job represents an HVAC service job
type Job struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	CustomerID  uint      `json:"customer_id" gorm:"not null"`
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description"`
	Status      string    `json:"status" gorm:"default:'pending'"`
	Priority    string    `json:"priority" gorm:"default:'medium'"`
	ScheduledAt *time.Time `json:"scheduled_at"`
	CompletedAt *time.Time `json:"completed_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	Customer    Customer  `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
	Quote       *Quote    `json:"quote,omitempty" gorm:"foreignKey:JobID"`
}

// Quote represents a service quote
type Quote struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	CustomerID  uint      `json:"customer_id" gorm:"not null"`
	JobID       *uint     `json:"job_id"`
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description"`
	Amount      float64   `json:"amount" gorm:"not null"`
	Status      string    `json:"status" gorm:"default:'draft'"`
	ValidUntil  *time.Time `json:"valid_until"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	Customer    Customer  `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
	Job         *Job      `json:"job,omitempty" gorm:"foreignKey:JobID"`
}// Email represents an email message
type Email struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	From        string    `json:"from" gorm:"not null"`
	To          string    `json:"to" gorm:"not null"`
	Subject     string    `json:"subject" gorm:"not null"`
	Body        string    `json:"body" gorm:"type:text"`
	HTMLBody    string    `json:"html_body" gorm:"type:text"`
	Status      string    `json:"status" gorm:"default:'pending'"`
	SentAt      *time.Time `json:"sent_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// AI Analysis
	Sentiment   string    `json:"sentiment"`
	Category    string    `json:"category"`
	Priority    string    `json:"priority"`
	Keywords    string    `json:"keywords" gorm:"type:text"`
}

// Memory represents stored context and knowledge
type Memory struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Key         string    `json:"key" gorm:"unique;not null"`
	Content     string    `json:"content" gorm:"type:text;not null"`
	Type        string    `json:"type" gorm:"not null"`
	Context     string    `json:"context" gorm:"type:text"`
	Embedding   string    `json:"embedding" gorm:"type:text"`
	Metadata    string    `json:"metadata" gorm:"type:json"`
	ExpiresAt   *time.Time `json:"expires_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// AIModel represents available AI models
type AIModel struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"unique;not null"`
	Provider    string    `json:"provider" gorm:"not null"`
	Type        string    `json:"type" gorm:"not null"`
	Endpoint    string    `json:"endpoint"`
	Status      string    `json:"status" gorm:"default:'active'"`
	Config      string    `json:"config" gorm:"type:json"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}