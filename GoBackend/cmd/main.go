package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"gobackend-hvac/internal/config"
	"gobackend-hvac/internal/database"
	"gobackend-hvac/internal/handlers"
	"gobackend-hvac/internal/middleware"
	"gobackend-hvac/internal/services"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Initialize services
	emailService := services.NewEmailService(cfg)
	aiService := services.NewAIService(cfg)
	memoryService := services.NewMemoryService(db)

	// Initialize handlers
	handlers := handlers.NewHandlers(emailService, aiService, memoryService)

	// Setup router
	router := gin.Default()
	
	// Add middleware
	router.Use(middleware.CORS())
	router.Use(middleware.Logger())
	router.Use(middleware.ErrorHandler())

	// Setup routes
	setupRoutes(router, handlers)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Starting GoBackend HVAC server on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

func setupRoutes(router *gin.Engine, h *handlers.Handlers) {
	api := router.Group("/api/v1")
	
	// Health check
	api.GET("/health", h.HealthCheck)
	
	// Email routes
	email := api.Group("/email")
	{
		email.POST("/send", h.SendEmail)
		email.GET("/analyze/:id", h.AnalyzeEmail)
		email.GET("/list", h.ListEmails)
	}
	
	// AI routes
	ai := api.Group("/ai")
	{
		ai.POST("/chat", h.AIChat)
		ai.POST("/analyze", h.AIAnalyze)
		ai.GET("/models", h.ListAIModels)
	}
	
	// Memory routes
	memory := api.Group("/memory")
	{
		memory.POST("/store", h.StoreMemory)
		memory.GET("/retrieve/:id", h.RetrieveMemory)
		memory.GET("/search", h.SearchMemory)
	}
	
	// HVAC specific routes
	hvac := api.Group("/hvac")
	{
		hvac.POST("/customers", h.CreateCustomer)
		hvac.GET("/customers", h.ListCustomers)
		hvac.POST("/jobs", h.CreateJob)
		hvac.GET("/jobs", h.ListJobs)
		hvac.POST("/quotes", h.CreateQuote)
		hvac.GET("/quotes", h.ListQuotes)
	}
}