#!/bin/bash

# GoBackend HVAC - Start Script
echo "🚀 Starting GoBackend HVAC System..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration before running again."
    exit 1
fi

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.24+ first."
    exit 1
fi

# Build the application
echo "🔨 Building application..."
go build -o main ./cmd/main.go

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

# Check database connection
echo "🔍 Checking database connection..."
if [ -z "$DATABASE_URL" ]; then
    echo "⚠️  DATABASE_URL not set. Using default PostgreSQL settings."
    export DATABASE_URL="postgres://hvac_user:hvac_password@localhost:5432/hvac_go?sslmode=disable"
fi

# Start the application
echo "✅ Starting GoBackend HVAC on port ${PORT:-8080}..."
echo "🌐 API will be available at: http://localhost:${PORT:-8080}/api/v1/health"
echo "📚 API Documentation: http://localhost:${PORT:-8080}/api/v1/"
echo ""
echo "Press Ctrl+C to stop the server"
echo "================================"

./main