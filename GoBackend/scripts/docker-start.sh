#!/bin/bash

# GoBackend HVAC - Docker Start Script
echo "🐳 Starting GoBackend HVAC with Docker..."

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null && ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your API keys before running again."
    echo ""
    echo "Required API keys:"
    echo "- OPENAI_API_KEY (for OpenAI GPT models)"
    echo "- ANTHROPIC_API_KEY (for Claude models)"
    echo "- HUGGINGFACE_TOKEN (for HuggingFace models)"
    echo ""
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Build and start services
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
echo "🔍 Checking service status..."
docker-compose ps

# Show logs
echo ""
echo "📋 Recent logs:"
docker-compose logs --tail=20

echo ""
echo "✅ GoBackend HVAC is starting up!"
echo "🌐 API Health Check: http://localhost:8080/api/v1/health"
echo "🐘 PostgreSQL: localhost:5432"
echo "🦙 Ollama: http://localhost:11434"
echo ""
echo "To view logs: docker-compose logs -f"
echo "To stop: docker-compose down"