version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: hvac_go
      POSTGRES_USER: hvac_user
      POSTGRES_PASSWORD: hvac_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvac_user -d hvac_go"]
      interval: 30s
      timeout: 10s
      retries: 3

  gobackend:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=************************************************/hvac_go?sslmode=disable
      - PORT=8080
      - ENVIRONMENT=development
      - JWT_SECRET=your-super-secret-jwt-key
      - SMTP_HOST=localhost
      - SMTP_PORT=587
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OLLAMA_URL=http://ollama:11434
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped

volumes:
  postgres_data:
  ollama_data: