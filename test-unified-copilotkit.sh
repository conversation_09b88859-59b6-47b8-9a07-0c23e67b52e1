#!/bin/bash

# HVAC-Remix + TruBackend + CopilotKit - System Test Script

echo "🧪 Testing HVAC-Remix with CopilotKit and TruBackend Integration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Test counter
total_tests=0
passed_tests=0

# Function to run test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_status="$3"
    
    total_tests=$((total_tests + 1))
    print_test "$test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        if [ "$expected_status" = "success" ]; then
            print_pass "$test_name"
            passed_tests=$((passed_tests + 1))
        else
            print_fail "$test_name (expected failure but got success)"
        fi
    else
        if [ "$expected_status" = "failure" ]; then
            print_pass "$test_name (expected failure)"
            passed_tests=$((passed_tests + 1))
        else
            print_fail "$test_name"
        fi
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_code="$3"
    
    total_tests=$((total_tests + 1))
    print_test "$name"
    
    response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" --max-time 10)
    
    if [ "$response_code" = "$expected_code" ]; then
        print_pass "$name (HTTP $response_code)"
        passed_tests=$((passed_tests + 1))
    else
        print_fail "$name (Expected HTTP $expected_code, got $response_code)"
    fi
}

echo ""
print_info "Starting comprehensive system tests..."
echo ""

# 1. Docker Infrastructure Tests
echo "🐳 Docker Infrastructure Tests"
echo "================================"

run_test "Docker daemon running" "docker info" "success"
run_test "Docker Compose available" "docker-compose --version" "success"

# 2. Container Status Tests
echo ""
echo "📦 Container Status Tests"
echo "========================="

containers=("postgres" "redis" "qdrant" "hvac-remix-copilotkit" "trubackend-orchestrator" "trubackend-memory" "trubackend-bielik" "trubackend-executive" "trubackend-langchain" "nginx")

for container in "${containers[@]}"; do
    run_test "$container container running" "docker-compose -f docker-compose.unified-copilotkit.yml ps | grep -q '$container.*Up'" "success"
done

# 3. Health Endpoint Tests
echo ""
echo "🏥 Health Endpoint Tests"
echo "========================"

test_endpoint "Main application health" "http://localhost/health" "200"
test_endpoint "TruBackend orchestrator health" "http://localhost/trubackend/health" "200"
test_endpoint "Memory Bank health" "http://localhost:8004/health" "200"
test_endpoint "Bielik integration health" "http://localhost:8005/health" "200"
test_endpoint "Executive Assistant health" "http://localhost:8003/health" "200"
test_endpoint "Langchain Automation health" "http://localhost:8002/health" "200"

# 4. Database Connectivity Tests
echo ""
echo "🗄️ Database Connectivity Tests"
echo "==============================="

run_test "PostgreSQL connection" "docker-compose -f docker-compose.unified-copilotkit.yml exec -T postgres pg_isready -U hvac_user -d hvac_crm" "success"
run_test "Redis connection" "docker-compose -f docker-compose.unified-copilotkit.yml exec -T redis redis-cli ping | grep -q PONG" "success"
test_endpoint "Qdrant vector database" "http://localhost:6333/health" "200"

# 5. CopilotKit Integration Tests
echo ""
echo "🤖 CopilotKit Integration Tests"
echo "==============================="

test_endpoint "CopilotKit runtime endpoint" "http://localhost/api/copilotkit" "405"  # Method not allowed for GET is expected
test_endpoint "HVAC-Remix main page" "http://localhost" "200"

# 6. TruBackend API Tests
echo ""
echo "🔧 TruBackend API Tests"
echo "======================="

# Test basic API endpoints
test_endpoint "TruBackend root API" "http://localhost/trubackend/" "200"
test_endpoint "Memory Bank API" "http://localhost:8004/api/health" "200"
test_endpoint "Bielik API status" "http://localhost:8005/api/status" "200"

# 7. Monitoring Tests
echo ""
echo "📊 Monitoring Tests"
echo "==================="

test_endpoint "Prometheus metrics" "http://localhost:9090/-/healthy" "200"
test_endpoint "Grafana dashboard" "http://localhost:3001/api/health" "200"

# 8. Network and Proxy Tests
echo ""
echo "🌐 Network and Proxy Tests"
echo "=========================="

test_endpoint "Nginx proxy health" "http://localhost/health" "200"
run_test "Internal network connectivity" "docker-compose -f docker-compose.unified-copilotkit.yml exec -T hvac-remix-copilotkit curl -s http://trubackend-orchestrator:8000/health" "success"

# 9. Performance Tests
echo ""
echo "⚡ Performance Tests"
echo "==================="

print_test "Response time test (main page)"
response_time=$(curl -o /dev/null -s -w "%{time_total}" http://localhost)
if (( $(echo "$response_time < 5.0" | bc -l) )); then
    print_pass "Response time: ${response_time}s (< 5s)"
    passed_tests=$((passed_tests + 1))
else
    print_fail "Response time: ${response_time}s (>= 5s)"
fi
total_tests=$((total_tests + 1))

# 10. Security Tests
echo ""
echo "🔒 Security Tests"
echo "================="

print_test "Security headers check"
security_headers=$(curl -s -I http://localhost | grep -E "(X-Frame-Options|X-Content-Type-Options|X-XSS-Protection)")
if [ -n "$security_headers" ]; then
    print_pass "Security headers present"
    passed_tests=$((passed_tests + 1))
else
    print_fail "Security headers missing"
fi
total_tests=$((total_tests + 1))

# 11. AI Model Tests (if available)
echo ""
echo "🧠 AI Model Tests"
echo "================="

print_test "Bielik V3 model availability"
bielik_status=$(curl -s http://localhost:8005/api/status | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
if [ "$bielik_status" = "ready" ] || [ "$bielik_status" = "healthy" ]; then
    print_pass "Bielik V3 model ready"
    passed_tests=$((passed_tests + 1))
else
    print_fail "Bielik V3 model not ready (status: $bielik_status)"
fi
total_tests=$((total_tests + 1))

# 12. Integration Tests
echo ""
echo "🔗 Integration Tests"
echo "===================="

print_test "CopilotKit to TruBackend communication"
# This would require a more complex test, for now just check if the bridge service is accessible
if docker-compose -f docker-compose.unified-copilotkit.yml exec -T hvac-remix-copilotkit test -f /app/app/services/copilotkit-bridge.server.ts; then
    print_pass "CopilotKit bridge service present"
    passed_tests=$((passed_tests + 1))
else
    print_fail "CopilotKit bridge service missing"
fi
total_tests=$((total_tests + 1))

# Final Results
echo ""
echo "📋 Test Results Summary"
echo "======================="
echo "Total Tests: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $((total_tests - passed_tests))"
echo "Success Rate: $(( passed_tests * 100 / total_tests ))%"

if [ $passed_tests -eq $total_tests ]; then
    echo ""
    print_pass "🎉 ALL TESTS PASSED! System is fully operational!"
    echo ""
    echo "✅ HVAC-Remix with CopilotKit and TruBackend is ready for use!"
    echo ""
    echo "🚀 Quick Access:"
    echo "   Main App:     http://localhost"
    echo "   Monitoring:   http://localhost:3001"
    echo "   Metrics:      http://localhost:9090"
    exit 0
else
    echo ""
    print_fail "❌ Some tests failed. Please check the logs and fix issues."
    echo ""
    echo "🔍 Debugging commands:"
    echo "   docker-compose -f docker-compose.unified-copilotkit.yml logs [service]"
    echo "   docker-compose -f docker-compose.unified-copilotkit.yml ps"
    echo "   curl -v http://localhost/health"
    exit 1
fi