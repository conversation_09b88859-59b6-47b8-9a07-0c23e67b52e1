#!/bin/bash

# HVAC-Remix + TruBackend + CopilotKit - Working Version Startup

echo "🚀 Starting HVAC-Remix with Copilot<PERSON>it (Working Version)..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Docker
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed."
    exit 1
fi

# Load environment variables
if [ -f ".env.unified-copilotkit" ]; then
    set -a
    source .env.unified-copilotkit
    set +a
    print_status "Environment loaded from .env.unified-copilotkit"
else
    print_warning "No .env.unified-copilotkit found, using defaults"
fi

# Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose -f docker-compose.working-copilotkit.yml down

# Build and start services
print_status "Building Docker images..."
docker-compose -f docker-compose.working-copilotkit.yml build

print_status "Starting infrastructure services..."
docker-compose -f docker-compose.working-copilotkit.yml up -d postgres redis

# Wait for infrastructure
print_status "Waiting for infrastructure services..."
sleep 15

# Check PostgreSQL
print_status "Checking PostgreSQL connection..."
for i in {1..30}; do
    if docker-compose -f docker-compose.working-copilotkit.yml exec -T postgres pg_isready -U hvac_user -d hvac_crm > /dev/null 2>&1; then
        print_success "PostgreSQL is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "PostgreSQL failed to start"
        exit 1
    fi
    sleep 2
done

# Start TruBackend
print_status "Starting TruBackend Simplified..."
docker-compose -f docker-compose.working-copilotkit.yml up -d trubackend-simplified

# Wait for TruBackend
print_status "Waiting for TruBackend..."
sleep 20

# Start HVAC-Remix
print_status "Starting HVAC-Remix with CopilotKit..."
docker-compose -f docker-compose.working-copilotkit.yml up -d hvac-remix

# Start Nginx
print_status "Starting Nginx proxy..."
docker-compose -f docker-compose.working-copilotkit.yml up -d nginx

# Final health check
print_status "Performing health checks..."
sleep 10

# Check services
services=("postgres" "redis" "trubackend-simplified" "hvac-remix" "nginx")
failed_services=()

for service in "${services[@]}"; do
    if docker-compose -f docker-compose.working-copilotkit.yml ps | grep -q "$service.*Up"; then
        print_success "$service is running"
    else
        print_error "$service failed to start"
        failed_services+=("$service")
    fi
done

if [ ${#failed_services[@]} -gt 0 ]; then
    print_error "Some services failed: ${failed_services[*]}"
    print_status "Check logs with: docker-compose -f docker-compose.working-copilotkit.yml logs [service]"
    exit 1
fi

# Success message
echo ""
print_success "🎉 HVAC-Remix with CopilotKit is running!"
echo ""
echo "📋 Access Information:"
echo "  🏠 Main Application:     http://localhost"
echo "  🤖 CopilotKit API:       http://localhost/api/copilotkit"
echo "  🔧 TruBackend API:       http://localhost/trubackend/"
echo "  📊 TruBackend Gradio:    http://localhost:8501"
echo ""
echo "🔍 Service Status:"
docker-compose -f docker-compose.working-copilotkit.yml ps
echo ""
print_success "System is ready! 🚀"