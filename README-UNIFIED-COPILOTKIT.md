# 🚀 HVAC-Remix + TruBackend + CopilotKit - Unified System

## 🎯 **Przegląd Systemu**

Kompletny, zunifikowany system HVAC CRM z zaawansowaną integracją AI, łączący:
- **HVAC-Remix** - Główna aplikacja CRM
- **TruBackend** - Zaawansowane usługi AI (Bielik V3, Memory Bank, Executive Assistant)
- **CopilotKit** - Naturalny interfejs AI z real-time streaming

### **🌟 Kluczowe Funkcjonalności:**
- 🤖 **Naturalny interfejs AI** - Rozmowa z asystentem w języku naturalnym
- 🧠 **Bielik V3 Integration** - Polski model AI z 8000 tokenów kontekstu
- 💾 **Memory Bank** - Inteligentna pamięć interakcji z klientami
- 📊 **Real-time Analytics** - Monitoring i metryki w czasie rzeczywistym
- 🔧 **HVAC-Specific Actions** - 8 specjalistycznych akcji AI dla branży HVAC
- 🌍 **Multi-language** - Wsparcie dla języka polskiego i angielskiego

---

## 🚀 **Szybki Start**

### **1. Wymagania Systemowe:**
- Docker 20.10+
- Docker Compose 2.0+
- NVIDIA GPU (dla Bielik V3)
- 16GB RAM (minimum)
- 50GB wolnego miejsca

### **2. Instalacja i Uruchomienie:**
```bash
# 1. Przejdź do katalogu głównego
cd /home/<USER>/HVAC

# 2. Skonfiguruj zmienne środowiskowe
cp .env.unified-copilotkit .env.local
# Edytuj .env.local i dodaj swoje klucze API

# 3. Uruchom cały system
./start-unified-copilotkit.sh

# 4. Przetestuj system
./test-unified-copilotkit.sh
```

### **3. Dostęp do Aplikacji:**
- **🏠 Główna aplikacja**: http://localhost
- **🤖 AI Assistant**: Sidebar w aplikacji głównej
- **📊 Monitoring**: http://localhost:3001 (admin/admin)
- **📈 Metryki**: http://localhost:9090

---

## 🏗️ **Architektura Systemu**

### **Komponenty Docker:**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HVAC-Remix    │    │   TruBackend    │    │   Monitoring    │
│   CopilotKit    │    │   AI Services   │    │   & Proxy       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Main CRM App  │    │ • Orchestrator  │    │ • Nginx Proxy   │
│ • CopilotKit UI │    │ • Memory Bank   │    │ • Prometheus    │
│ • AI Actions    │    │ • Bielik V3     │    │ • Grafana       │
│ • Dashboard     │    │ • Executive AI  │    │ • Health Checks │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────┐
         │            Infrastructure                   │
         ├─────────────────────────────────────────────┤
         │ • PostgreSQL (Multi-DB)                     │
         │ • Redis Cache                               │
         │ • Qdrant Vector DB                          │
         │ • Docker Network                            │
         └─────────────────────────────────────────────┘
```

### **Porty i Usługi:**
| Usługa | Port | Opis |
|--------|------|------|
| HVAC-Remix | 3000 | Główna aplikacja CRM |
| TruBackend Orchestrator | 8000 | Orkiestrator AI |
| Langchain Automation | 8002 | Automatyzacja procesów |
| Executive Assistant | 8003 | Asystent wykonawczy |
| Memory Bank | 8004 | Bank pamięci |
| Bielik Integration | 8005 | Model Bielik V3 |
| PostgreSQL | 5432 | Baza danych |
| Redis | 6379 | Cache |
| Qdrant | 6333 | Baza wektorowa |
| Nginx | 80 | Reverse proxy |
| Prometheus | 9090 | Metryki |
| Grafana | 3001 | Dashboard |

---

## 🤖 **Funkcjonalności CopilotKit**

### **Dostępne Akcje AI:**

#### **1. analyzeCustomerIssue**
- Kompleksowa analiza problemów HVAC
- Integracja z Bielik V3 dla analizy technicznej
- Wyszukiwanie podobnych przypadków w Memory Bank
- Generowanie rekomendacji i ocena pilności

#### **2. searchCustomerHistory**
- Przeszukiwanie historii interakcji z klientem
- Analiza wzorców zachowań
- Kontekst dla personalizowanych odpowiedzi

#### **3. askBielikExpert**
- Bezpośrednia konsultacja z modelem Bielik V3
- Analiza semantyczna, wykrywanie intencji
- Klasyfikacja techniczna problemów HVAC

#### **4. generateProfessionalResponse**
- Generowanie profesjonalnych odpowiedzi email
- Dostosowanie tonu (profesjonalny/przyjazny/pilny)
- Opcje harmonogramowania

#### **5. optimizeServiceRoute**
- Optymalizacja tras serwisowych
- Minimalizacja czasu i kosztów
- Uwzględnienie priorytetów i umiejętności techników

#### **6. predictMaintenanceNeeds**
- Predykcja potrzeb konserwacyjnych
- Analiza danych urządzeń
- Rekomendacje harmonogramu konserwacji

#### **7. analyzeDocument**
- Analiza dokumentów HVAC (instrukcje, faktury, zdjęcia)
- OCR i ekstrakcja danych
- Klasyfikacja i indeksowanie

#### **8. checkSystemStatus**
- Monitoring zdrowia wszystkich usług TruBackend
- Real-time status check
- Alerty o problemach

---

## 📊 **Monitoring i Metryki**

### **Grafana Dashboards:**
- **System Overview** - Ogólny przegląd systemu
- **HVAC-Remix Metrics** - Metryki aplikacji głównej
- **TruBackend AI Services** - Monitoring usług AI
- **Database Performance** - Wydajność baz danych
- **User Activity** - Aktywność użytkowników

### **Kluczowe Metryki:**
- Response time aplikacji (cel: <30s)
- AI confidence scores (cel: >90%)
- System uptime (cel: 99.9%)
- Error rates (cel: <1%)
- GPU utilization (Bielik V3)
- Memory usage (Memory Bank)

---

## 🔧 **Zarządzanie Systemem**

### **Podstawowe Komendy:**
```bash
# Uruchomienie systemu
./start-unified-copilotkit.sh

# Testowanie systemu
./test-unified-copilotkit.sh

# Sprawdzenie statusu
docker-compose -f docker-compose.unified-copilotkit.yml ps

# Logi konkretnej usługi
docker-compose -f docker-compose.unified-copilotkit.yml logs -f [service_name]

# Restart usługi
docker-compose -f docker-compose.unified-copilotkit.yml restart [service_name]

# Zatrzymanie systemu
docker-compose -f docker-compose.unified-copilotkit.yml down
```

### **Backup i Recovery:**
```bash
# Backup bazy danych
docker-compose -f docker-compose.unified-copilotkit.yml exec postgres pg_dump -U hvac_user hvac_crm > backup.sql

# Backup Memory Bank
docker-compose -f docker-compose.unified-copilotkit.yml exec postgres pg_dump -U hvac_user trubackend_memory > memory_backup.sql

# Restore
docker-compose -f docker-compose.unified-copilotkit.yml exec -T postgres psql -U hvac_user hvac_crm < backup.sql
```

---

## 🔒 **Bezpieczeństwo**

### **Konfiguracja Bezpieczeństwa:**
- **Rate Limiting** - Ograniczenia dla API CopilotKit i TruBackend
- **CORS Headers** - Właściwa konfiguracja CORS
- **Security Headers** - XSS, CSRF, Frame Options protection
- **Network Isolation** - Izolacja kontenerów w sieci Docker
- **API Key Management** - Bezpieczne przechowywanie kluczy

### **Zmienne Środowiskowe:**
```bash
# Wymagane klucze API
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_TOKEN=your_huggingface_token_here

# Opcjonalne
COPILOTKIT_API_KEY=your_copilotkit_api_key_here
LANGSMITH_API_KEY=your_langsmith_api_key_here
```

---

## 🚀 **Deployment Production**

### **Przygotowanie do Produkcji:**
1. **SSL Configuration** - Konfiguracja HTTPS w Nginx
2. **Environment Variables** - Ustawienie production env vars
3. **Database Backup** - Automatyczne backupy
4. **Monitoring Alerts** - Konfiguracja alertów
5. **Load Balancing** - Skalowanie horyzontalne
6. **Security Hardening** - Dodatkowe zabezpieczenia

### **Skalowanie:**
```bash
# Skalowanie HVAC-Remix
docker-compose -f docker-compose.unified-copilotkit.yml up -d --scale hvac-remix=3

# Load balancer configuration w Nginx
# Automatyczne rozłożenie ruchu między instancje
```

---

## 🔍 **Rozwiązywanie Problemów**

### **Typowe Problemy:**

#### **Usługi nie startują:**
```bash
# Sprawdź zasoby systemowe
docker system df
docker system prune

# Sprawdź logi
docker-compose -f docker-compose.unified-copilotkit.yml logs
```

#### **Brak połączenia z TruBackend:**
```bash
# Test connectivity
curl http://localhost/trubackend/health
docker-compose -f docker-compose.unified-copilotkit.yml exec hvac-remix curl http://trubackend-orchestrator:8000/health
```

#### **Problemy z GPU (Bielik):**
```bash
# Sprawdź GPU
nvidia-smi
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
```

### **Health Checks:**
```bash
# Sprawdź wszystkie endpointy
curl http://localhost/health
curl http://localhost/trubackend/health
curl http://localhost:8004/health
curl http://localhost:8005/health
```

---

## 📚 **Dokumentacja**

### **Dodatkowe Pliki:**
- `DOCKER_UNIFIED_COPILOTKIT_GUIDE.md` - Szczegółowy przewodnik Docker
- `COPILOTKIT_MIGRATION_PLAN.md` - Plan migracji z Agent Protocol
- `COPILOTKIT_MIGRATION_SUMMARY.md` - Podsumowanie migracji
- `docs/copilotkit/` - Dokumentacja techniczna CopilotKit

### **API Documentation:**
- CopilotKit Actions: `/docs/copilotkit/actions.md`
- TruBackend API: `/docs/trubackend/api.md`
- Health Endpoints: `/docs/monitoring/health.md`

---

## 🎉 **Podsumowanie**

Ten zunifikowany system zapewnia:

✅ **Kompletną integrację AI** - HVAC-Remix + TruBackend + CopilotKit
✅ **Naturalny interfejs** - Rozmowa z AI w języku naturalnym
✅ **Zaawansowane AI** - Bielik V3, Memory Bank, Executive Assistant
✅ **Production Ready** - Monitoring, bezpieczeństwo, skalowanie
✅ **Jednokomendowe uruchomienie** - Cały system w Docker
✅ **Comprehensive Testing** - Automatyczne testy systemu

**System jest gotowy do użycia w środowisku produkcyjnym i zapewnia najnowocześniejszą integrację AI dla branży HVAC!** 🚀

---

*Dokumentacja systemu HVAC-Remix CopilotKit Unified*  
*Data: Styczeń 2025*  
*Wersja: 1.0.0*  
*Status: Production Ready* ✅