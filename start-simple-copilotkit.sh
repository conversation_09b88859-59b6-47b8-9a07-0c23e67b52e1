#!/bin/bash

# HVAC-Remix + CopilotKit - Simple Version Startup

echo "🚀 Starting HVAC-Remix with Copilot<PERSON>it (Simple Version)..."

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Docker
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running"
    exit 1
fi

# Load environment
if [ -f ".env.unified-copilotkit" ]; then
    set -a
    source .env.unified-copilotkit
    set +a
    print_status "Environment loaded"
fi

# Check if TruBackend is running
print_status "Checking if TruBackend is available..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    print_success "TruBackend is running on port 8000"
else
    print_status "TruBackend not detected. Starting TruBackend Simplified..."
    cd TruBackend
    ./start-simplified.sh &
    cd ..
    sleep 10
fi

# Stop any existing containers
print_status "Stopping existing containers..."
docker-compose -f docker-compose.simple-copilotkit.yml down

# Build and start
print_status "Building HVAC-Remix..."
docker-compose -f docker-compose.simple-copilotkit.yml build hvac-remix

print_status "Starting infrastructure..."
docker-compose -f docker-compose.simple-copilotkit.yml up -d postgres redis

# Wait for infrastructure
sleep 10

print_status "Starting HVAC-Remix with CopilotKit..."
docker-compose -f docker-compose.simple-copilotkit.yml up -d hvac-remix

# Health check
sleep 15
print_status "Checking services..."

if docker-compose -f docker-compose.simple-copilotkit.yml ps | grep -q "hvac-remix.*Up"; then
    print_success "HVAC-Remix is running!"
    echo ""
    echo "📋 Access Information:"
    echo "  🏠 HVAC-Remix:        http://localhost:3001"
    echo "  🤖 CopilotKit API:    http://localhost:3001/api/copilotkit"
    echo "  🔧 TruBackend:        http://localhost:8000"
    echo ""
    print_success "System ready! 🚀"
else
    print_error "Failed to start HVAC-Remix"
    docker-compose -f docker-compose.simple-copilotkit.yml logs hvac-remix
fi