global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # HVAC-Remix Application
  - job_name: 'hvac-remix'
    static_configs:
      - targets: ['hvac-remix-copilotkit:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # TruBackend Orchestrator
  - job_name: 'trubackend-orchestrator'
    static_configs:
      - targets: ['trubackend-orchestrator:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # TruBackend Memory Bank
  - job_name: 'trubackend-memory'
    static_configs:
      - targets: ['trubackend-memory:8004']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # TruBackend Bielik Integration
  - job_name: 'trubackend-bielik'
    static_configs:
      - targets: ['trubackend-bielik:8005']
    metrics_path: '/metrics'
    scrape_interval: 60s

  # Tru<PERSON><PERSON>end Executive Assistant
  - job_name: 'trubackend-executive'
    static_configs:
      - targets: ['trubackend-executive:8003']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # TruBackend Langchain Automation
  - job_name: 'trubackend-langchain'
    static_configs:
      - targets: ['trubackend-langchain:8002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Qdrant Vector Database
  - job_name: 'qdrant'
    static_configs:
      - targets: ['qdrant:6333']
    metrics_path: '/metrics'
    scrape_interval: 30s