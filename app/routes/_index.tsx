import { json, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { 
  TrendingUp, 
  Users, 
  Activity, 
  DollarSign,
  AlertCircle,
  CheckCircle 
} from "lucide-react";

// Główna filozofia: "Customer Success First"
// Każdy element interfejsu pokazuje wpływ na sukces klienta

export async function loader({ request }: LoaderFunctionArgs) {
  // Pobieranie kluczowych metryk sukcesu klienta
  const successMetrics = {
    satisfactionScore: 4.8,
    activeCustomers: 1247,
    resolvedIssues: 98.5,
    revenueGrowth: 23.4,
    systemUptime: 99.9,
    avgResponseTime: 12 // minuty
  };

  return json({ successMetrics });
}

export default function CRMDashboard() {
  const { successMetrics } = useLoaderData<typeof loader>();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto p-6">
        {/* Nagłówek z filozofią */}
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            HVAC CRM - Centrum Sukcesu Klienta
          </h1>
          <p className="text-lg text-gray-600">
            Każda decyzja, każda akcja - wszystko dla maksymalizacji wartości dla Twoich klientów
          </p>
        </header>

        {/* Kluczowe metryki sukcesu */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <MetricCard
            icon={<Users className="w-8 h-8" />}
            title="Aktywni Klienci"
            value={successMetrics.activeCustomers}
            trend="+12%"
            color="blue"
          />
          <MetricCard
            icon={<Activity className="w-8 h-8" />}
            title="Satysfakcja Klientów"
            value={`${successMetrics.satisfactionScore}/5`}
            trend="+0.3"
            color="green"
          />
          <MetricCard
            icon={<DollarSign className="w-8 h-8" />}
            title="Wzrost Przychodów"
            value={`${successMetrics.revenueGrowth}%`}
            trend="+5.2%"
            color="indigo"
          />
        </div>

        {/* Główne moduły CRM */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <ModuleCard
            title="Zarządzanie Klientami"
            description="Kompletna baza z historią interakcji"
            link="/customers"
            icon={<Users />}
          />
          <ModuleCard
            title="Instalacje HVAC"
            description="Monitoring systemów i serwis"
            link="/installations"
            icon={<Activity />}
          />
          <ModuleCard
            title="Zgłoszenia Serwisowe"
            description="Błyskawiczne reagowanie na potrzeby"
            link="/tickets"
            icon={<AlertCircle />}
          />
          <ModuleCard
            title="Analityka Sukcesu"
            description="Insights prowadzące do wzrostu"
            link="/analytics"
            icon={<TrendingUp />}
          />
        </div>
      </div>
    </div>
  );
}

function MetricCard({ icon, title, value, trend, color }: any) {
  const colorClasses = {
    blue: "bg-blue-500",
    green: "bg-green-500",
    indigo: "bg-indigo-500"
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 transform transition hover:scale-105">
      <div className={`${colorClasses[color]} text-white p-3 rounded-lg inline-block mb-4`}>
        {icon}
      </div>
      <h3 className="text-gray-600 text-sm font-medium">{title}</h3>
      <div className="flex items-baseline justify-between">
        <p className="text-2xl font-bold text-gray-800">{value}</p>
        <span className="text-green-500 text-sm font-medium">{trend}</span>
      </div>
    </div>
  );
}

function ModuleCard({ title, description, link, icon }: any) {
  return (
    <a
      href={link}
      className="block bg-white rounded-lg shadow-md p-6 hover:shadow-xl transition-all hover:-translate-y-1"
    >
      <div className="text-indigo-500 mb-4">{icon}</div>
      <h3 className="font-semibold text-lg mb-2">{title}</h3>
      <p className="text-gray-600 text-sm">{description}</p>
    </a>
  );
}